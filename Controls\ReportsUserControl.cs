using System;
using System.Windows.Forms;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Controls
{
    public partial class ReportsUserControl : UserControl
    {
        private readonly SimpleDataManager dataManager;
        private Label lblTitle;
        private TabControl tabControl;
        private TabPage tabSales;
        private TabPage tabProducts;
        private TabPage tabCustomers;
        private TabPage tabSuppliers;
        private DateTimePicker dtpFrom;
        private DateTimePicker dtpTo;
        private Button btnGenerate;
        private Button btnExport;
        private DataGridView gridReport;

        public ReportsUserControl()
        {
            dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.tabControl = new TabControl();
            this.tabSales = new TabPage();
            this.tabProducts = new TabPage();
            this.tabCustomers = new TabPage();
            this.tabSuppliers = new TabPage();
            this.dtpFrom = new DateTimePicker();
            this.dtpTo = new DateTimePicker();
            this.btnGenerate = new Button();
            this.btnExport = new Button();
            this.gridReport = new DataGridView();

            // إعداد العنوان
            lblTitle.Text = "التقارير";
            lblTitle.Font = new System.Drawing.Font("Arial", 16, System.Drawing.FontStyle.Bold);
            lblTitle.Location = new System.Drawing.Point(20, 20);
            lblTitle.AutoSize = true;

            // إعداد التبويبات
            tabControl.Location = new System.Drawing.Point(20, 60);
            tabControl.Size = new System.Drawing.Size(880, 500);

            tabSales.Text = "تقرير المبيعات";
            tabProducts.Text = "تقرير المنتجات";
            tabCustomers.Text = "تقرير العملاء";
            tabSuppliers.Text = "تقرير الموردين";

            tabControl.TabPages.Add(tabSales);
            tabControl.TabPages.Add(tabProducts);
            tabControl.TabPages.Add(tabCustomers);
            tabControl.TabPages.Add(tabSuppliers);

            // إعداد أدوات التقرير
            dtpFrom.Location = new System.Drawing.Point(20, 20);
            dtpFrom.Size = new System.Drawing.Size(150, 25);
            dtpFrom.Format = DateTimePickerFormat.Short;
            dtpFrom.Value = DateTime.Today.AddMonths(-1);

            dtpTo.Location = new System.Drawing.Point(190, 20);
            dtpTo.Size = new System.Drawing.Size(150, 25);
            dtpTo.Format = DateTimePickerFormat.Short;
            dtpTo.Value = DateTime.Today;

            btnGenerate.Text = "توليد التقرير";
            btnGenerate.Location = new System.Drawing.Point(360, 20);
            btnGenerate.Size = new System.Drawing.Size(120, 25);
            btnGenerate.Click += BtnGenerate_Click;

            btnExport.Text = "تصدير";
            btnExport.Location = new System.Drawing.Point(500, 20);
            btnExport.Size = new System.Drawing.Size(120, 25);
            btnExport.Click += BtnExport_Click;

            // إعداد جدول التقرير
            gridReport.Location = new System.Drawing.Point(20, 60);
            gridReport.Size = new System.Drawing.Size(840, 400);
            gridReport.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            gridReport.ReadOnly = true;
            gridReport.AllowUserToAddRows = false;
            gridReport.AllowUserToDeleteRows = false;
            gridReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            gridReport.MultiSelect = false;

            // إضافة عناصر التحكم
            tabSales.Controls.Add(dtpFrom);
            tabSales.Controls.Add(dtpTo);
            tabSales.Controls.Add(btnGenerate);
            tabSales.Controls.Add(btnExport);
            tabSales.Controls.Add(gridReport);

            this.Controls.Add(lblTitle);
            this.Controls.Add(tabControl);
        }

        private void LoadData()
        {
            // تحميل تقرير المبيعات افتراضياً
            LoadSalesReport();
        }

        private void LoadSalesReport()
        {
            var report = dataManager.GetSalesReport(dtpFrom.Value, dtpTo.Value);
            gridReport.DataSource = report;
        }

        private void LoadProductsReport()
        {
            var report = dataManager.GetProductsReport(dtpFrom.Value, dtpTo.Value);
            gridReport.DataSource = report;
        }

        private void LoadCustomersReport()
        {
            var report = dataManager.GetCustomersReport(dtpFrom.Value, dtpTo.Value);
            gridReport.DataSource = report;
        }

        private void LoadSuppliersReport()
        {
            var report = dataManager.GetSuppliersReport(dtpFrom.Value, dtpTo.Value);
            gridReport.DataSource = report;
        }

        private void BtnGenerate_Click(object sender, EventArgs e)
        {
            switch (tabControl.SelectedIndex)
            {
                case 0:
                    LoadSalesReport();
                    break;
                case 1:
                    LoadProductsReport();
                    break;
                case 2:
                    LoadCustomersReport();
                    break;
                case 3:
                    LoadSuppliersReport();
                    break;
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            var dialog = new SaveFileDialog();
            dialog.Filter = "Excel Files|*.xlsx";
            dialog.Title = "تصدير التقرير";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                switch (tabControl.SelectedIndex)
                {
                    case 0:
                        var salesData = dataManager.GetSalesReport(dtpFrom.Value, dtpTo.Value);
                        dataManager.ExportSalesReportToExcel(salesData, dialog.FileName);
                        break;
                    case 1:
                        var productsData = dataManager.GetProductsReport(dtpFrom.Value, dtpTo.Value);
                        dataManager.ExportProductsReportToExcel(productsData, dialog.FileName);
                        break;
                    case 2:
                        // ExportCustomersToExcel in SimpleDataManager handles fetching data by date range
                        dataManager.ExportCustomersToExcel(dtpFrom.Value, dtpTo.Value, dialog.FileName);
                        break;
                    case 3:
                        // ExportSuppliersToExcel in SimpleDataManager handles fetching data by date range
                        dataManager.ExportSuppliersToExcel(dtpFrom.Value, dtpTo.Value, dialog.FileName);
                        break;
                }
                MessageBox.Show("تم تصدير التقرير بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }
} 