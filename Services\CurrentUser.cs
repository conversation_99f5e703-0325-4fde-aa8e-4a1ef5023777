using System;

namespace SimpleAccounting.Services
{
    public static class CurrentUser
    {
        public static string? Username { get; private set; }
        public static string? DisplayName { get; private set; }
        public static bool IsLoggedIn { get; private set; }

        public static void SetUser(string username, string displayName)
        {
            Username = username;
            DisplayName = displayName;
            IsLoggedIn = true;
        }

        public static void Logout()
        {
            Username = null;
            DisplayName = null;
            IsLoggedIn = false;
        }
    }
} 