using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Forms
{
    public partial class StockAdjustmentForm : Form
    {
        private readonly SimpleDataManager dataManager;
        private readonly SimpleProduct product;
        private Label lblProduct;
        private Label lblCurrentQuantity;
        private NumericUpDown numQuantity;
        private ComboBox cmbType;
        private TextBox txtNotes;
        private Button btnSave;
        private Button btnCancel;

        public StockAdjustmentForm(SimpleProduct product)
        {
            this.dataManager = new SimpleDataManager();
            this.product = product;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تسوية المخزون";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Product Info
            this.lblProduct = new Label
            {
                Text = $"المنتج: {product.Name}",
                Location = new Point(20, 20),
                Size = new Size(340, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.lblCurrentQuantity = new Label
            {
                Text = $"الكمية الحالية: {product.Quantity}",
                Location = new Point(20, 60),
                Size = new Size(340, 30),
                Font = new Font("Segoe UI", 12),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Adjustment Type
            var lblType = new Label
            {
                Text = "نوع التسوية:",
                Location = new Point(20, 100),
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.cmbType = new ComboBox
            {
                Location = new Point(130, 100),
                Size = new Size(230, 30),
                Font = new Font("Segoe UI", 12),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            this.cmbType.Items.AddRange(new object[] { "إضافة", "خصم", "تسوية" });
            this.cmbType.SelectedIndex = 0;

            // Quantity
            var lblQuantity = new Label
            {
                Text = "الكمية:",
                Location = new Point(20, 140),
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.numQuantity = new NumericUpDown
            {
                Location = new Point(130, 140),
                Size = new Size(230, 30),
                Font = new Font("Segoe UI", 12),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2,
                Value = 0
            };

            // Notes
            var lblNotes = new Label
            {
                Text = "ملاحظات:",
                Location = new Point(20, 180),
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.txtNotes = new TextBox
            {
                Location = new Point(130, 180),
                Size = new Size(230, 30),
                Font = new Font("Segoe UI", 12)
            };

            // Buttons
            this.btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(130, 220),
                Size = new Size(100, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.Click += BtnSave_Click;

            this.btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(240, 220),
                Size = new Size(100, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblProduct,
                this.lblCurrentQuantity,
                lblType,
                this.cmbType,
                lblQuantity,
                this.numQuantity,
                lblNotes,
                this.txtNotes,
                this.btnSave,
                this.btnCancel
            });
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (numQuantity.Value == 0)
            {
                MessageBox.Show("الرجاء إدخال كمية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            decimal newQuantity;
            switch (cmbType.SelectedIndex)
            {
                case 0: // Add
                    newQuantity = product.Quantity + (decimal)numQuantity.Value;
                    break;
                case 1: // Subtract
                    newQuantity = product.Quantity - (decimal)numQuantity.Value;
                    if (newQuantity < 0)
                    {
                        MessageBox.Show("لا يمكن أن تكون الكمية أقل من صفر", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    break;
                case 2: // Set
                    newQuantity = (decimal)numQuantity.Value;
                    break;
                default:
                    return;
            }

            // Update product quantity
            product.Quantity = newQuantity;
            dataManager.UpdateProduct(product);

            // Add stock movement record
            string movementType = cmbType.SelectedIndex == 0 ? "إضافة" : cmbType.SelectedIndex == 1 ? "خصم" : "تسوية";
            dataManager.AddStockMovement(
                product.Id,
                movementType,
                (int)numQuantity.Value,
                txtNotes.Text
            );

            this.DialogResult = DialogResult.OK;
        }
    }
} 