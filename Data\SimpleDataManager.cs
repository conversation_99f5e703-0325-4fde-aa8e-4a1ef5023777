using System;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Collections.Generic;
using SimpleAccounting.Models;
using System.Linq;
using OfficeOpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Drawing;
using ClosedXML.Excel;
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;

namespace SimpleAccounting.Data
{
    public class SimpleDataManager
    {
        private readonly string dbPath;
        private readonly string connectionString;
        private List<SimpleProduct> _products;
        private List<SimpleCustomer> _customers;
        private List<SimpleSupplier> _suppliers;
        private List<SimpleInvoice> _invoices;
        private List<SimplePayment> _payments;
        private List<SimpleInvoiceItem> _invoiceItems;
        private List<SimpleCategory> _categories;

        public SimpleDataManager()
        {
            dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SimpleAccounting.db");
            connectionString = $"Data Source={dbPath};Version=3;";
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            if (!File.Exists(dbPath))
            {
                SQLiteConnection.CreateFile(dbPath);
                using var connection = new SQLiteConnection(connectionString);
                connection.Open();

                // Create Products table
                ExecuteNonQuery(connection, @"
                    CREATE TABLE Products (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Code TEXT NOT NULL,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        Barcode TEXT,
                        CategoryId INTEGER,
                        SupplierId INTEGER,
                        PurchasePrice REAL NOT NULL,
                        Price REAL NOT NULL,
                        Quantity INTEGER NOT NULL DEFAULT 0,
                        MinQuantity INTEGER NOT NULL DEFAULT 0,
                        MaxQuantity INTEGER NOT NULL DEFAULT 0,
                        Unit TEXT NOT NULL
                    )");

                // Create Categories table
                ExecuteNonQuery(connection, @"
                    CREATE TABLE Categories (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        CreatedAt DATETIME NOT NULL,
                        UpdatedAt DATETIME NOT NULL
                    )");

                // Create Suppliers table
                ExecuteNonQuery(connection, @"
                    CREATE TABLE Suppliers (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Phone TEXT NOT NULL,
                        Email TEXT NOT NULL,
                        Address TEXT,
                        Balance REAL NOT NULL DEFAULT 0
                    )");

                // Create Customers table
                ExecuteNonQuery(connection, @"
                    CREATE TABLE Customers (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Phone TEXT,
                        Email TEXT,
                        Address TEXT,
                        Balance REAL NOT NULL DEFAULT 0
                    )");

                // Create Invoices table
                ExecuteNonQuery(connection, @"
                    CREATE TABLE Invoices (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        InvoiceNumber TEXT NOT NULL,
                        CustomerId INTEGER,
                        CustomerName TEXT NOT NULL,
                        InvoiceDate DATETIME NOT NULL,
                        SubTotal REAL NOT NULL,
                        TaxAmount REAL NOT NULL,
                        TotalAmount REAL NOT NULL,
                        FOREIGN KEY (CustomerId) REFERENCES Customers(Id)
                    )");

                // Create InvoiceItems table
                ExecuteNonQuery(connection, @"
                    CREATE TABLE InvoiceItems (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        InvoiceId INTEGER NOT NULL,
                        ProductId INTEGER NOT NULL,
                        ProductName TEXT NOT NULL,
                        Quantity INTEGER NOT NULL,
                        UnitPrice REAL NOT NULL,
                        DiscountPercentage REAL NOT NULL DEFAULT 0,
                        LineTotal REAL NOT NULL,
                        FOREIGN KEY (InvoiceId) REFERENCES Invoices(Id),
                        FOREIGN KEY (ProductId) REFERENCES Products(Id)
                    )");

                // Create StockMovements table
                ExecuteNonQuery(connection, @"
                    CREATE TABLE StockMovements (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ProductId INTEGER NOT NULL,
                        MovementDate DATETIME NOT NULL,
                        MovementType TEXT NOT NULL,
                        QuantityChanged INTEGER NOT NULL,
                        Notes TEXT,
                        FOREIGN KEY (ProductId) REFERENCES Products(Id)
                    )");

                // Create Settings table
                ExecuteNonQuery(connection, @"
                    CREATE TABLE Settings (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        CompanyName TEXT NOT NULL,
                        DefaultTaxRate REAL NOT NULL DEFAULT 15,
                        LastInvoiceNumber INTEGER NOT NULL DEFAULT 0
                    )");

                // Insert default settings
                ExecuteNonQuery(connection, @"
                    INSERT INTO Settings (CompanyName, DefaultTaxRate, LastInvoiceNumber)
                    VALUES ('شركتي', 15, 0)");
            }
        }

        private void ExecuteNonQuery(SQLiteConnection connection, string commandText)
        {
            using var command = new SQLiteCommand(commandText, connection);
            command.ExecuteNonQuery();
        }

        // Product Methods
        public List<SimpleProduct> GetAllProducts()
        {
            var products = new List<SimpleProduct>();
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            using var command = new SQLiteCommand("SELECT * FROM Products", connection);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                products.Add(new SimpleProduct
                {
                    Id = reader.GetInt32(0),
                    Code = reader.GetString(1),
                    Name = reader.GetString(2),
                    Description = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                    Barcode = reader.IsDBNull(4) ? string.Empty : reader.GetString(4),
                    CategoryId = reader.IsDBNull(5) ? 0 : reader.GetInt32(5),
                    SupplierId = reader.IsDBNull(6) ? 0 : reader.GetInt32(6),
                    PurchasePrice = reader.GetDecimal(7),
                    Price = reader.GetDecimal(8),
                    Quantity = reader.GetInt32(9),
                    MinQuantity = reader.GetInt32(10),
                    MaxQuantity = reader.GetInt32(11),
                    Unit = reader.GetString(12)
                });
            }

            return products;
        }

        public SimpleProduct? GetProductById(int id)
        {
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            using var command = new SQLiteCommand("SELECT * FROM Products WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);

            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SimpleProduct
                {
                    Id = reader.GetInt32(0),
                    Code = reader.GetString(1),
                    Name = reader.GetString(2),
                    Description = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                    Barcode = reader.IsDBNull(4) ? string.Empty : reader.GetString(4),
                    CategoryId = reader.IsDBNull(5) ? 0 : reader.GetInt32(5),
                    SupplierId = reader.IsDBNull(6) ? 0 : reader.GetInt32(6),
                    PurchasePrice = reader.GetDecimal(7),
                    Price = reader.GetDecimal(8),
                    Quantity = reader.GetInt32(9),
                    MinQuantity = reader.GetInt32(10),
                    MaxQuantity = reader.GetInt32(11),
                    Unit = reader.GetString(12)
                };
            }

            return null;
        }

        public void AddProduct(SimpleProduct product)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"INSERT INTO Products (Code, Name, Description, CategoryId, SupplierId, Price, PurchasePrice, 
                Quantity, MinimumQuantity, MaximumQuantity, SellingPrice, CreatedAt, UpdatedAt)
                VALUES (@Code, @Name, @Description, @CategoryId, @SupplierId, @Price, @PurchasePrice, 
                @Quantity, @MinimumQuantity, @MaximumQuantity, @SellingPrice, @CreatedAt, @UpdatedAt)", connection);

            command.Parameters.AddWithValue("@Code", product.Code);
            command.Parameters.AddWithValue("@Name", product.Name);
            command.Parameters.AddWithValue("@Description", product.Description);
            command.Parameters.AddWithValue("@CategoryId", product.CategoryId);
            command.Parameters.AddWithValue("@SupplierId", product.SupplierId);
            command.Parameters.AddWithValue("@Price", product.Price);
            command.Parameters.AddWithValue("@PurchasePrice", product.PurchasePrice);
            command.Parameters.AddWithValue("@Quantity", product.Quantity);
            command.Parameters.AddWithValue("@MinimumQuantity", product.MinimumQuantity);
            command.Parameters.AddWithValue("@MaximumQuantity", product.MaximumQuantity);
            command.Parameters.AddWithValue("@SellingPrice", product.SellingPrice);
            command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        public void UpdateProduct(SimpleProduct product)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"UPDATE Products 
                SET Code = @Code,
                    Name = @Name,
                    Description = @Description,
                    CategoryId = @CategoryId,
                    SupplierId = @SupplierId,
                    Price = @Price,
                    PurchasePrice = @PurchasePrice,
                    Quantity = @Quantity,
                    MinimumQuantity = @MinimumQuantity,
                    MaximumQuantity = @MaximumQuantity,
                    SellingPrice = @SellingPrice,
                    UpdatedAt = @UpdatedAt
                WHERE Id = @Id", connection);

            command.Parameters.AddWithValue("@Id", product.Id);
            command.Parameters.AddWithValue("@Code", product.Code);
            command.Parameters.AddWithValue("@Name", product.Name);
            command.Parameters.AddWithValue("@Description", product.Description);
            command.Parameters.AddWithValue("@CategoryId", product.CategoryId);
            command.Parameters.AddWithValue("@SupplierId", product.SupplierId);
            command.Parameters.AddWithValue("@Price", product.Price);
            command.Parameters.AddWithValue("@PurchasePrice", product.PurchasePrice);
            command.Parameters.AddWithValue("@Quantity", product.Quantity);
            command.Parameters.AddWithValue("@MinimumQuantity", product.MinimumQuantity);
            command.Parameters.AddWithValue("@MaximumQuantity", product.MaximumQuantity);
            command.Parameters.AddWithValue("@SellingPrice", product.SellingPrice);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        public void DeleteProduct(int id)
        {
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            using var command = new SQLiteCommand("DELETE FROM Products WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            command.ExecuteNonQuery();
        }

        public List<SimpleProduct> SearchProducts(string searchTerm)
        {
            var products = new List<SimpleProduct>();
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            using var command = new SQLiteCommand(@"
                SELECT * FROM Products 
                WHERE Code LIKE @SearchTerm 
                OR Name LIKE @SearchTerm 
                OR Barcode LIKE @SearchTerm", connection);

            command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                products.Add(new SimpleProduct
                {
                    Id = reader.GetInt32(0),
                    Code = reader.GetString(1),
                    Name = reader.GetString(2),
                    Description = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                    Barcode = reader.IsDBNull(4) ? string.Empty : reader.GetString(4),
                    CategoryId = reader.IsDBNull(5) ? 0 : reader.GetInt32(5),
                    SupplierId = reader.IsDBNull(6) ? 0 : reader.GetInt32(6),
                    PurchasePrice = reader.GetDecimal(7),
                    Price = reader.GetDecimal(8),
                    Quantity = reader.GetInt32(9),
                    MinQuantity = reader.GetInt32(10),
                    MaxQuantity = reader.GetInt32(11),
                    Unit = reader.GetString(12)
                });
            }

            return products;
        }

        public List<SimpleProduct> GetProducts()
        {
            var products = new List<SimpleProduct>();
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Products", connection);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                products.Add(new SimpleProduct
                {
                    Id = reader.GetInt32(0),
                    Code = reader.GetString(1),
                    Name = reader.GetString(2),
                    Description = reader.GetString(3),
                    CategoryId = reader.GetInt32(4),
                    SupplierId = reader.GetInt32(5),
                    Price = reader.GetDecimal(6),
                    PurchasePrice = reader.GetDecimal(7),
                    Quantity = reader.GetInt32(8),
                    MinimumQuantity = reader.GetInt32(9),
                    MaximumQuantity = reader.GetInt32(10),
                    SellingPrice = reader.GetDecimal(11),
                    CreatedAt = reader.GetDateTime(12),
                    UpdatedAt = reader.GetDateTime(13)
                });
            }
            return products;
        }

        public SimpleProduct GetProduct(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Products WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SimpleProduct
                {
                    Id = reader.GetInt32(0),
                    Code = reader.GetString(1),
                    Name = reader.GetString(2),
                    Description = reader.GetString(3),
                    CategoryId = reader.GetInt32(4),
                    SupplierId = reader.GetInt32(5),
                    Price = reader.GetDecimal(6),
                    PurchasePrice = reader.GetDecimal(7),
                    Quantity = reader.GetInt32(8),
                    MinimumQuantity = reader.GetInt32(9),
                    MaximumQuantity = reader.GetInt32(10),
                    SellingPrice = reader.GetDecimal(11),
                    CreatedAt = reader.GetDateTime(12),
                    UpdatedAt = reader.GetDateTime(13)
                };
            }
            return null;
        }

        // Customer Methods
        public List<SimpleCustomer> GetAllCustomers()
        {
            var customers = new List<SimpleCustomer>();
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            using var command = new SQLiteCommand("SELECT * FROM Customers", connection);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                customers.Add(new SimpleCustomer
                {
                    Id = reader.GetInt32(0),
                    Name = reader.GetString(1),
                    Phone = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                    Email = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                    Address = reader.IsDBNull(4) ? string.Empty : reader.GetString(4),
                    Balance = reader.GetDecimal(5)
                });
            }

            return customers;
        }

        public SimpleCustomer? GetCustomerById(int id)
        {
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            using var command = new SQLiteCommand("SELECT * FROM Customers WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);

            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SimpleCustomer
                {
                    Id = reader.GetInt32(0),
                    Name = reader.GetString(1),
                    Phone = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                    Email = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                    Address = reader.IsDBNull(4) ? string.Empty : reader.GetString(4),
                    Balance = reader.GetDecimal(5)
                };
            }

            return null;
        }

        public void AddCustomer(SimpleCustomer customer)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"INSERT INTO Customers (Name, Phone, Email, Address, CreatedAt, UpdatedAt)
                VALUES (@Name, @Phone, @Email, @Address, @CreatedAt, @UpdatedAt)", connection);

            command.Parameters.AddWithValue("@Name", customer.Name);
            command.Parameters.AddWithValue("@Phone", customer.Phone);
            command.Parameters.AddWithValue("@Email", customer.Email);
            command.Parameters.AddWithValue("@Address", customer.Address);
            command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        public void UpdateCustomer(SimpleCustomer customer)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"UPDATE Customers 
                SET Name = @Name,
                    Phone = @Phone,
                    Email = @Email,
                    Address = @Address,
                    UpdatedAt = @UpdatedAt
                WHERE Id = @Id", connection);

            command.Parameters.AddWithValue("@Id", customer.Id);
            command.Parameters.AddWithValue("@Name", customer.Name);
            command.Parameters.AddWithValue("@Phone", customer.Phone);
            command.Parameters.AddWithValue("@Email", customer.Email);
            command.Parameters.AddWithValue("@Address", customer.Address);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        public void DeleteCustomer(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("DELETE FROM Customers WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            command.ExecuteNonQuery();
        }

        public List<SimpleCustomer> SearchCustomers(string searchTerm)
        {
            var customers = new List<SimpleCustomer>();
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            using var command = new SQLiteCommand(@"
                SELECT * FROM Customers 
                WHERE Name LIKE @SearchTerm 
                OR Phone LIKE @SearchTerm 
                OR Email LIKE @SearchTerm", connection);

            command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                customers.Add(new SimpleCustomer
                {
                    Id = reader.GetInt32(0),
                    Name = reader.GetString(1),
                    Phone = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                    Email = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                    Address = reader.IsDBNull(4) ? string.Empty : reader.GetString(4),
                    Balance = reader.GetDecimal(5)
                });
            }

            return customers;
        }

        public void UpdateCustomerBalance(int customerId, decimal amount)
        {
            using var connection = new SQLiteConnection(connectionString);
            connection.Open();

            using var command = new SQLiteCommand(@"
                UPDATE Customers 
                SET Balance = Balance + @Amount
                WHERE Id = @Id", connection);

            command.Parameters.AddWithValue("@Id", customerId);
            command.Parameters.AddWithValue("@Amount", amount);

            command.ExecuteNonQuery();
        }

        // Supplier Methods
        public List<SimpleSupplier> GetAllSuppliers()
        {
            var suppliers = new List<SimpleSupplier>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand("SELECT * FROM Suppliers", connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            suppliers.Add(new SimpleSupplier
                            {
                                Id = reader.GetInt32(0),
                                Name = reader.GetString(1),
                                Phone = reader.GetString(2),
                                Email = reader.GetString(3),
                                Address = reader.GetString(4),
                                Balance = reader.GetDecimal(5)
                            });
                        }
                    }
                }
            }
            return suppliers;
        }

        public SimpleSupplier? GetSupplierById(int id)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand("SELECT * FROM Suppliers WHERE Id = @Id", connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new SimpleSupplier
                            {
                                Id = reader.GetInt32(0),
                                Name = reader.GetString(1),
                                Phone = reader.GetString(2),
                                Email = reader.GetString(3),
                                Address = reader.GetString(4),
                                Balance = reader.GetDecimal(5)
                            };
                        }
                    }
                }
            }
            return null;
        }

        public void AddSupplier(SimpleSupplier supplier)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(
                    "INSERT INTO Suppliers (Name, Phone, Email, Address, Balance) " +
                    "VALUES (@Name, @Phone, @Email, @Address, @Balance)", connection))
                {
                    command.Parameters.AddWithValue("@Name", supplier.Name);
                    command.Parameters.AddWithValue("@Phone", supplier.Phone);
                    command.Parameters.AddWithValue("@Email", supplier.Email);
                    command.Parameters.AddWithValue("@Address", supplier.Address);
                    command.Parameters.AddWithValue("@Balance", supplier.Balance);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdateSupplier(SimpleSupplier supplier)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"UPDATE Suppliers 
                SET Name = @Name,
                    ContactPerson = @ContactPerson,
                    Phone = @Phone,
                    Email = @Email,
                    Address = @Address,
                    UpdatedAt = @UpdatedAt
                WHERE Id = @Id", connection);

            command.Parameters.AddWithValue("@Id", supplier.Id);
            command.Parameters.AddWithValue("@Name", supplier.Name);
            command.Parameters.AddWithValue("@ContactPerson", supplier.ContactPerson);
            command.Parameters.AddWithValue("@Phone", supplier.Phone);
            command.Parameters.AddWithValue("@Email", supplier.Email);
            command.Parameters.AddWithValue("@Address", supplier.Address);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        public void DeleteSupplier(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("DELETE FROM Suppliers WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            command.ExecuteNonQuery();
        }

        public List<SimpleSupplier> SearchSuppliers(string searchTerm)
        {
            var suppliers = new List<SimpleSupplier>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(
                    "SELECT * FROM Suppliers WHERE Name LIKE @SearchTerm OR Phone LIKE @SearchTerm OR Email LIKE @SearchTerm",
                    connection))
                {
                    command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            suppliers.Add(new SimpleSupplier
                            {
                                Id = reader.GetInt32(0),
                                Name = reader.GetString(1),
                                Phone = reader.GetString(2),
                                Email = reader.GetString(3),
                                Address = reader.GetString(4),
                                Balance = reader.GetDecimal(5)
                            });
                        }
                    }
                }
            }
            return suppliers;
        }

        public void UpdateSupplierBalance(int supplierId, decimal amount)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(
                    "UPDATE Suppliers SET Balance = Balance + @Amount WHERE Id = @Id", connection))
                {
                    command.Parameters.AddWithValue("@Id", supplierId);
                    command.Parameters.AddWithValue("@Amount", amount);
                    command.ExecuteNonQuery();
                }
            }
        }

        public List<SimpleInvoice> GetAllInvoices()
        {
            var invoices = new List<SimpleInvoice>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(
                    "SELECT i.*, c.Name as CustomerName FROM Invoices i " +
                    "LEFT JOIN Customers c ON i.CustomerId = c.Id " +
                    "ORDER BY i.Date DESC", connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var invoice = new SimpleInvoice
                            {
                                Id = reader.GetInt32(0),
                                InvoiceNumber = reader.GetString(1),
                                Date = reader.GetDateTime(2),
                                CustomerId = reader.GetInt32(3),
                                CustomerName = reader.GetString(8),
                                SubTotal = reader.GetDecimal(4),
                                Tax = reader.GetDecimal(5),
                                Discount = reader.GetDecimal(6),
                                Total = reader.GetDecimal(7),
                                Notes = reader.GetString(9)
                            };
                            invoice.Items = GetInvoiceItems(invoice.Id);
                            invoices.Add(invoice);
                        }
                    }
                }
            }
            return invoices;
        }

        public SimpleInvoice? GetInvoiceById(int id)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(
                    "SELECT i.*, c.Name as CustomerName FROM Invoices i " +
                    "LEFT JOIN Customers c ON i.CustomerId = c.Id " +
                    "WHERE i.Id = @Id", connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var invoice = new SimpleInvoice
                            {
                                Id = reader.GetInt32(0),
                                InvoiceNumber = reader.GetString(1),
                                Date = reader.GetDateTime(2),
                                CustomerId = reader.GetInt32(3),
                                CustomerName = reader.GetString(8),
                                SubTotal = reader.GetDecimal(4),
                                Tax = reader.GetDecimal(5),
                                Discount = reader.GetDecimal(6),
                                Total = reader.GetDecimal(7),
                                Notes = reader.GetString(9)
                            };
                            invoice.Items = GetInvoiceItems(invoice.Id);
                            return invoice;
                        }
                    }
                }
            }
            return null;
        }

        // GetInvoiceItems: دالة للحصول على عناصر الفاتورة
        public List<SimpleInvoiceItem> GetInvoiceItems(int invoiceId)
        {
            var items = new List<SimpleInvoiceItem>();
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM InvoiceItems WHERE InvoiceId = @InvoiceId", connection);
            command.Parameters.AddWithValue("@InvoiceId", invoiceId);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                items.Add(new SimpleInvoiceItem
                {
                    Id = reader.GetInt32(0),
                    InvoiceId = reader.GetInt32(1),
                    ProductId = reader.GetInt32(2),
                    ProductName = reader.GetString(3),
                    ProductCode = reader.GetString(4),
                    Quantity = reader.GetInt32(5),
                    UnitPrice = reader.GetDecimal(6),
                    Discount = reader.GetDecimal(7),
                    Total = reader.GetDecimal(8),
                    CreatedAt = reader.GetDateTime(9)
                });
            }
            return items;
        }

        public void AddInvoice(SimpleInvoice invoice)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"INSERT INTO Invoices (InvoiceNumber, CustomerId, CustomerName, Date, SubTotal, Tax, Discount, Total, CreatedAt, UpdatedAt)
                VALUES (@InvoiceNumber, @CustomerId, @CustomerName, @Date, @SubTotal, @Tax, @Discount, @Total, @CreatedAt, @UpdatedAt)", connection);

            command.Parameters.AddWithValue("@InvoiceNumber", invoice.InvoiceNumber);
            command.Parameters.AddWithValue("@CustomerId", invoice.CustomerId);
            command.Parameters.AddWithValue("@CustomerName", invoice.CustomerName);
            command.Parameters.AddWithValue("@Date", invoice.Date);
            command.Parameters.AddWithValue("@SubTotal", invoice.SubTotal);
            command.Parameters.AddWithValue("@Tax", invoice.Tax);
            command.Parameters.AddWithValue("@Discount", invoice.Discount);
            command.Parameters.AddWithValue("@Total", invoice.Total);
            command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        public void DeleteInvoice(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("DELETE FROM Invoices WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            command.ExecuteNonQuery();
        }

        public List<SimpleInvoice> SearchInvoices(string searchTerm)
        {
            var invoices = new List<SimpleInvoice>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(
                    "SELECT i.*, c.Name as CustomerName FROM Invoices i " +
                    "LEFT JOIN Customers c ON i.CustomerId = c.Id " +
                    "WHERE i.InvoiceNumber LIKE @SearchTerm OR c.Name LIKE @SearchTerm " +
                    "ORDER BY i.Date DESC", connection))
                {
                    command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var invoice = new SimpleInvoice
                            {
                                Id = reader.GetInt32(0),
                                InvoiceNumber = reader.GetString(1),
                                Date = reader.GetDateTime(2),
                                CustomerId = reader.GetInt32(3),
                                CustomerName = reader.GetString(8),
                                SubTotal = reader.GetDecimal(4),
                                Tax = reader.GetDecimal(5),
                                Discount = reader.GetDecimal(6),
                                Total = reader.GetDecimal(7),
                                Notes = reader.GetString(9)
                            };
                            invoice.Items = GetInvoiceItems(invoice.Id);
                            invoices.Add(invoice);
                        }
                    }
                }
            }
            return invoices;
        }

        public string GenerateInvoiceNumber()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(
                    "SELECT COUNT(*) FROM Invoices WHERE strftime('%Y', Date) = strftime('%Y', 'now')", connection))
                {
                    var count = Convert.ToInt32(command.ExecuteScalar());
                    return $"INV-{DateTime.Now:yyyy}-{count + 1:D6}";
                }
            }
        }

        // Settings Management
        public SimpleSettings GetSettings()
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Settings", connection);
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SimpleSettings
                {
                    Id = reader.GetInt32(0),
                    CompanyName = reader.GetString(1),
                    CompanyAddress = reader.GetString(2),
                    CompanyPhone = reader.GetString(3),
                    CompanyEmail = reader.GetString(4),
                    CompanyWebsite = reader.GetString(5),
                    CompanyLogo = reader.GetString(6),
                    TaxRate = reader.GetDecimal(7),
                    Currency = reader.GetString(8),
                    CreatedAt = reader.GetDateTime(9),
                    UpdatedAt = reader.GetDateTime(10)
                };
            }
            return null;
        }

        public void UpdateSettings(SimpleSettings settings)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"UPDATE Settings 
                SET CompanyName = @CompanyName,
                    CompanyAddress = @CompanyAddress,
                    CompanyPhone = @CompanyPhone,
                    CompanyEmail = @CompanyEmail,
                    CompanyWebsite = @CompanyWebsite,
                    CompanyLogo = @CompanyLogo,
                    TaxRate = @TaxRate,
                    Currency = @Currency,
                    UpdatedAt = @UpdatedAt
                WHERE Id = @Id", connection);

            command.Parameters.AddWithValue("@Id", settings.Id);
            command.Parameters.AddWithValue("@CompanyName", settings.CompanyName);
            command.Parameters.AddWithValue("@CompanyAddress", settings.CompanyAddress);
            command.Parameters.AddWithValue("@CompanyPhone", settings.CompanyPhone);
            command.Parameters.AddWithValue("@CompanyEmail", settings.CompanyEmail);
            command.Parameters.AddWithValue("@CompanyWebsite", settings.CompanyWebsite);
            command.Parameters.AddWithValue("@CompanyLogo", settings.CompanyLogo);
            command.Parameters.AddWithValue("@TaxRate", settings.TaxRate);
            command.Parameters.AddWithValue("@Currency", settings.Currency);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        // User Management
        public List<SimpleUser> GetAllUsers()
        {
            var users = new List<SimpleUser>();
            using var conn = GetConnection();
            conn.Open();
            using var cmd = new SQLiteCommand("SELECT * FROM Users", conn);
            using var reader = cmd.ExecuteReader();
            while (reader.Read())
            {
                users.Add(new SimpleUser
                {
                    Id = Convert.ToInt32(reader["Id"]),
                    Username = reader["Username"].ToString(),
                    Password = reader["Password"].ToString(),
                    FullName = reader["FullName"].ToString(),
                    Role = reader["Role"].ToString(),
                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                    CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString()),
                    LastLogin = reader["LastLogin"] == DBNull.Value ? null : DateTime.Parse(reader["LastLogin"].ToString())
                });
            }
            return users;
        }

        public void AddUser(SimpleUser user)
        {
            using var conn = GetConnection();
            conn.Open();
            using var cmd = new SQLiteCommand(@"INSERT INTO Users (Username, Password, FullName, Role, IsActive, CreatedAt) VALUES (@Username, @Password, @FullName, @Role, @IsActive, @CreatedAt)", conn);
            cmd.Parameters.AddWithValue("@Username", user.Username);
            cmd.Parameters.AddWithValue("@Password", user.Password);
            cmd.Parameters.AddWithValue("@FullName", user.FullName);
            cmd.Parameters.AddWithValue("@Role", user.Role);
            cmd.Parameters.AddWithValue("@IsActive", user.IsActive);
            cmd.Parameters.AddWithValue("@CreatedAt", user.CreatedAt);
            cmd.ExecuteNonQuery();
        }

        public void UpdateUser(SimpleUser user)
        {
            using var conn = GetConnection();
            conn.Open();
            using var cmd = new SQLiteCommand(@"UPDATE Users SET FullName = @FullName, Role = @Role, IsActive = @IsActive WHERE Id = @Id", conn);
            cmd.Parameters.AddWithValue("@FullName", user.FullName);
            cmd.Parameters.AddWithValue("@Role", user.Role);
            cmd.Parameters.AddWithValue("@IsActive", user.IsActive);
            cmd.Parameters.AddWithValue("@Id", user.Id);
            cmd.ExecuteNonQuery();
        }

        public void DeleteUser(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("DELETE FROM Users WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            command.ExecuteNonQuery();
        }

        public void UpdateUserLastLogin(int userId)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"UPDATE Users 
                SET LastLogin = @LastLogin
                WHERE Id = @Id", connection);

            command.Parameters.AddWithValue("@Id", userId);
            command.Parameters.AddWithValue("@LastLogin", DateTime.Now);

            command.ExecuteNonQuery();
        }

        // GetConnection: دالة للحصول على اتصال قاعدة البيانات
        private SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(connectionString);
        }

        // BackupDatabase: دالة لعمل نسخة احتياطية من قاعدة البيانات
        public void BackupDatabase(string backupPath)
        {
            if (File.Exists(dbPath))
            {
                File.Copy(dbPath, backupPath, true);
            }
        }

        // RestoreDatabase: دالة لاستعادة قاعدة البيانات من نسخة احتياطية
        public void RestoreDatabase(string backupPath)
        {
            if (File.Exists(backupPath))
            {
                File.Copy(backupPath, dbPath, true);
            }
        }

        // AddStockMovement: دالة لإضافة حركة مخزون
        public void AddStockMovement(int productId, string movementType, int quantityChanged, string notes)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                "INSERT INTO StockMovements (ProductId, MovementDate, MovementType, QuantityChanged, Notes) VALUES (@ProductId, @MovementDate, @MovementType, @QuantityChanged, @Notes)",
                connection);
            command.Parameters.AddWithValue("@ProductId", productId);
            command.Parameters.AddWithValue("@MovementDate", DateTime.Now);
            command.Parameters.AddWithValue("@MovementType", movementType);
            command.Parameters.AddWithValue("@QuantityChanged", quantityChanged);
            command.Parameters.AddWithValue("@Notes", notes);
            command.ExecuteNonQuery();
        }

        // AddPayment: دالة لإضافة دفعة
        public void AddPayment(SimplePayment payment)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"INSERT INTO Payments (CustomerId, Amount, PaymentType, Notes, PaymentDate, CreatedAt, UpdatedAt)
                VALUES (@CustomerId, @Amount, @PaymentType, @Notes, @PaymentDate, @CreatedAt, @UpdatedAt)", connection);

            command.Parameters.AddWithValue("@CustomerId", payment.CustomerId);
            command.Parameters.AddWithValue("@Amount", payment.Amount);
            command.Parameters.AddWithValue("@PaymentType", payment.PaymentType);
            command.Parameters.AddWithValue("@Notes", payment.Notes);
            command.Parameters.AddWithValue("@PaymentDate", payment.PaymentDate);
            command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        // UpdateInvoice: دالة لتحديث الفاتورة
        public void UpdateInvoice(SimpleInvoice invoice)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"UPDATE Invoices 
                SET InvoiceNumber = @InvoiceNumber,
                    CustomerId = @CustomerId,
                    CustomerName = @CustomerName,
                    Date = @Date,
                    SubTotal = @SubTotal,
                    Tax = @Tax,
                    Discount = @Discount,
                    Total = @Total,
                    UpdatedAt = @UpdatedAt
                WHERE Id = @Id", connection);

            command.Parameters.AddWithValue("@Id", invoice.Id);
            command.Parameters.AddWithValue("@InvoiceNumber", invoice.InvoiceNumber);
            command.Parameters.AddWithValue("@CustomerId", invoice.CustomerId);
            command.Parameters.AddWithValue("@CustomerName", invoice.CustomerName);
            command.Parameters.AddWithValue("@Date", invoice.Date);
            command.Parameters.AddWithValue("@InvoiceDate", invoice.Date);
            command.Parameters.AddWithValue("@SubTotal", invoice.SubTotal);
            command.Parameters.AddWithValue("@TaxAmount", invoice.Tax);
            command.Parameters.AddWithValue("@TotalAmount", invoice.Total);
            command.Parameters.AddWithValue("@Id", invoice.Id);
            command.ExecuteNonQuery();
        }

        // DeleteInvoiceItems: دالة لحذف عناصر الفاتورة
        public void DeleteInvoiceItems(int invoiceId)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("DELETE FROM InvoiceItems WHERE InvoiceId = @InvoiceId", connection);
            command.Parameters.AddWithValue("@InvoiceId", invoiceId);
            command.ExecuteNonQuery();
        }

        // AddInvoiceItem: دالة لإضافة عنصر فاتورة
        public void AddInvoiceItem(SimpleInvoiceItem item)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"INSERT INTO InvoiceItems (InvoiceId, ProductId, ProductName, ProductCode, Quantity, UnitPrice, Discount, Total, CreatedAt)
                VALUES (@InvoiceId, @ProductId, @ProductName, @ProductCode, @Quantity, @UnitPrice, @Discount, @Total, @CreatedAt)", connection);

            command.Parameters.AddWithValue("@InvoiceId", item.InvoiceId);
            command.Parameters.AddWithValue("@ProductId", item.ProductId);
            command.Parameters.AddWithValue("@ProductName", item.ProductName);
            command.Parameters.AddWithValue("@ProductCode", item.ProductCode);
            command.Parameters.AddWithValue("@Quantity", item.Quantity);
            command.Parameters.AddWithValue("@UnitPrice", item.UnitPrice);
            command.Parameters.AddWithValue("@Discount", item.Discount);
            command.Parameters.AddWithValue("@Total", item.Total);
            command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        // ExportInvoicesToExcel: دالة لتصدير الفواتير إلى ملف Excel
        public void ExportInvoicesToExcel(string filePath)
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("Invoices");

            // Add headers
            worksheet.Cell(1, 1).Value = "رقم الفاتورة";
            worksheet.Cell(1, 2).Value = "التاريخ";
            worksheet.Cell(1, 3).Value = "العميل";
            worksheet.Cell(1, 4).Value = "المجموع";
            worksheet.Cell(1, 5).Value = "الضريبة";
            worksheet.Cell(1, 6).Value = "الإجمالي";

            // Add data
            var invoices = GetAllInvoices();
            for (int i = 0; i < invoices.Count; i++)
            {
                var invoice = invoices[i];
                worksheet.Cell(i + 2, 1).Value = invoice.InvoiceNumber;
                worksheet.Cell(i + 2, 2).Value = invoice.Date;
                worksheet.Cell(i + 2, 3).Value = invoice.CustomerName;
                worksheet.Cell(i + 2, 4).Value = invoice.SubTotal;
                worksheet.Cell(i + 2, 5).Value = invoice.Tax;
                worksheet.Cell(i + 2, 6).Value = invoice.Total;
            }

            // Save the file
            workbook.SaveAs(filePath);
        }

        // GetNextInvoiceNumber: دالة للحصول على رقم الفاتورة التالي
        public string GetNextInvoiceNumber()
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT MAX(CAST(SUBSTR(InvoiceNumber, 2) AS INTEGER)) FROM Invoices", connection);
            var result = command.ExecuteScalar();
            int nextNumber = 1;
            if (result != null && result != DBNull.Value)
            {
                nextNumber = Convert.ToInt32(result) + 1;
            }
            return $"I{nextNumber:D6}";
        }

        public List<SimpleUser> GetUsers()
        {
            var users = new List<SimpleUser>();
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Users", connection);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                users.Add(new SimpleUser
                {
                    Id = reader.GetInt32(0),
                    Username = reader.GetString(1),
                    Password = reader.GetString(2),
                    FullName = reader.GetString(3),
                    Role = reader.GetString(4),
                    IsActive = reader.GetBoolean(5),
                    CreatedAt = reader.GetDateTime(6),
                    LastLogin = reader.IsDBNull(7) ? null : (DateTime?)reader.GetDateTime(7)
                });
            }
            return users;
        }

        public List<SimpleCategory> GetCategories()
        {
            var categories = new List<SimpleCategory>();
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Categories", connection);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                categories.Add(new SimpleCategory
                {
                    Id = reader.GetInt32(0),
                    Name = reader.GetString(1),
                    Description = reader.GetString(2),
                    CreatedAt = reader.GetDateTime(3),
                    UpdatedAt = reader.GetDateTime(4)
                });
            }
            return categories;
        }

        public SimpleCategory GetCategory(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Categories WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SimpleCategory
                {
                    Id = reader.GetInt32(0),
                    Name = reader.GetString(1),
                    Description = reader.GetString(2),
                    CreatedAt = reader.GetDateTime(3),
                    UpdatedAt = reader.GetDateTime(4)
                };
            }
            return null;
        }

        public void AddCategory(SimpleCategory category)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand(
                @"INSERT INTO Categories (Name, Description, CreatedAt, UpdatedAt)
                VALUES (@Name, @Description, @CreatedAt, @UpdatedAt)", connection);

            command.Parameters.AddWithValue("@Name", category.Name);
            command.Parameters.AddWithValue("@Description", category.Description);
            command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            command.ExecuteNonQuery();
        }

        public void DeleteCategory(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("DELETE FROM Categories WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            command.ExecuteNonQuery();
        }

        public List<SimpleCustomer> GetCustomers()
        {
            var customers = new List<SimpleCustomer>();
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Customers", connection);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                customers.Add(new SimpleCustomer
                {
                    Id = reader.GetInt32(0),
                    Name = reader.GetString(1),
                    Phone = reader.GetString(2),
                    Email = reader.GetString(3),
                    Address = reader.GetString(4),
                    CreatedAt = reader.GetDateTime(5),
                    UpdatedAt = reader.GetDateTime(6)
                });
            }
            return customers;
        }

        public SimpleCustomer GetCustomer(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Customers WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SimpleCustomer
                {
                    Id = reader.GetInt32(0),
                    Name = reader.GetString(1),
                    Phone = reader.GetString(2),
                    Email = reader.GetString(3),
                    Address = reader.GetString(4),
                    CreatedAt = reader.GetDateTime(5),
                    UpdatedAt = reader.GetDateTime(6)
                };
            }
            return null;
        }

        public List<SimpleInvoice> GetInvoices()
        {
            var invoices = new List<SimpleInvoice>();
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Invoices", connection);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                invoices.Add(new SimpleInvoice
                {
                    Id = reader.GetInt32(0),
                    InvoiceNumber = reader.GetString(1),
                    CustomerId = reader.GetInt32(2),
                    CustomerName = reader.GetString(3),
                    Date = reader.GetDateTime(4),
                    SubTotal = reader.GetDecimal(5),
                    Tax = reader.GetDecimal(6),
                    Discount = reader.GetDecimal(7),
                    Total = reader.GetDecimal(8),
                    CreatedAt = reader.GetDateTime(9),
                    UpdatedAt = reader.GetDateTime(10)
                });
            }
            return invoices;
        }

        public SimpleInvoice GetInvoice(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Invoices WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SimpleInvoice
                {
                    Id = reader.GetInt32(0),
                    InvoiceNumber = reader.GetString(1),
                    CustomerId = reader.GetInt32(2),
                    CustomerName = reader.GetString(3),
                    Date = reader.GetDateTime(4),
                    SubTotal = reader.GetDecimal(5),
                    Tax = reader.GetDecimal(6),
                    Discount = reader.GetDecimal(7),
                    Total = reader.GetDecimal(8),
                    CreatedAt = reader.GetDateTime(9),
                    UpdatedAt = reader.GetDateTime(10)
                };
            }
            return null;
        }

        public List<SimpleStockMovement> GetStockMovements()
        {
            var movements = new List<SimpleStockMovement>();
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM StockMovements", connection);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                movements.Add(new SimpleStockMovement
                {
                    Id = reader.GetInt32(0),
                    ProductId = reader.GetInt32(1),
                    MovementDate = reader.GetDateTime(2),
                    MovementType = reader.GetString(3),
                    QuantityChanged = reader.GetInt32(4),
                    Notes = reader.GetString(5),
                    CreatedAt = reader.GetDateTime(6),
                    UpdatedAt = reader.GetDateTime(7)
                });
            }
            return movements;
        }

        public SimpleStockMovement GetStockMovement(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM StockMovements WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SimpleStockMovement
                {
                    Id = reader.GetInt32(0),
                    ProductId = reader.GetInt32(1),
                    MovementDate = reader.GetDateTime(2),
                    MovementType = reader.GetString(3),
                    QuantityChanged = reader.GetInt32(4),
                    Notes = reader.GetString(5),
                    CreatedAt = reader.GetDateTime(6),
                    UpdatedAt = reader.GetDateTime(7)
                };
            }
            return null;
        }

        public List<SimplePayment> GetPayments()
        {
            var payments = new List<SimplePayment>();
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Payments", connection);
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                payments.Add(new SimplePayment
                {
                    Id = reader.GetInt32(0),
                    CustomerId = reader.GetInt32(1),
                    Amount = reader.GetDecimal(2),
                    PaymentType = reader.GetString(3),
                    Notes = reader.GetString(4),
                    PaymentDate = reader.GetDateTime(5),
                    CreatedAt = reader.GetDateTime(6),
                    UpdatedAt = reader.GetDateTime(7)
                });
            }
            return payments;
        }

        public SimplePayment GetPayment(int id)
        {
            using var connection = GetConnection();
            connection.Open();
            using var command = new SQLiteCommand("SELECT * FROM Payments WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", id);
            using var reader = command.ExecuteReader();
            if (reader.Read())
            {
                return new SimplePayment
                {
                    Id = reader.GetInt32(0),
                    CustomerId = reader.GetInt32(1),
                    Amount = reader.GetDecimal(2),
                    PaymentType = reader.GetString(3),
                    Notes = reader.GetString(4),
                    PaymentDate = reader.GetDateTime(5),
                    CreatedAt = reader.GetDateTime(6),
                    UpdatedAt = reader.GetDateTime(7)
                };
            }
            return null;
        }

        public DashboardStats GetDashboardStats()
        {
            var stats = new DashboardStats
            {
                TotalSales = _invoices?.Sum(i => i.Total) ?? 0,
                TotalCustomers = _customers?.Count ?? 0,
                TotalSuppliers = _suppliers?.Count ?? 0,
                TotalProducts = _products?.Count ?? 0,
                TotalStock = _products?.Sum(p => p.Quantity) ?? 0,
                TotalPayments = _payments?.Sum(p => p.Amount) ?? 0
            };
            return stats;
        }

        public List<SimpleInvoice> GetRecentSales(int count = 10)
        {
            return _invoices.OrderByDescending(i => i.Date).Take(count).ToList();
        }

        public List<SimpleProduct> GetLowStockProducts()
        {
            return _products.Where(p => p.Quantity <= p.MinQuantity).ToList();
        }

        public List<SimpleInvoice> GetSalesReport(DateTime fromDate, DateTime toDate)
        {
            return _invoices.Where(i => i.Date >= fromDate && i.Date <= toDate).ToList();
        }

        public List<SimpleProduct> GetProductsReport(DateTime fromDate, DateTime toDate)
        {
            return _products.Where(p => p.CreatedAt >= fromDate && p.CreatedAt <= toDate).ToList();
        }

        public List<SimpleCustomer> GetCustomersReport(DateTime fromDate, DateTime toDate)
        {
            return _customers.Where(c => c.CreatedAt >= fromDate && c.CreatedAt <= toDate).ToList();
        }

        public List<SimpleSupplier> GetSuppliersReport(DateTime fromDate, DateTime toDate)
        {
            return _suppliers.Where(s => s.CreatedAt >= fromDate && s.CreatedAt <= toDate).ToList();
        }

        public void ExportSalesReportToExcel(List<SimpleInvoice> invoices, string filePath)
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("تقرير المبيعات");

            // إضافة العناوين
            worksheet.Cell(1, 1).Value = "رقم الفاتورة";
            worksheet.Cell(1, 2).Value = "التاريخ";
            worksheet.Cell(1, 3).Value = "العميل";
            worksheet.Cell(1, 4).Value = "الإجمالي";
            worksheet.Cell(1, 5).Value = "الحالة";

            // إضافة البيانات
            int row = 2;
            foreach (var invoice in invoices)
            {
                worksheet.Cell(row, 1).Value = invoice.Number;
                worksheet.Cell(row, 2).Value = invoice.Date;
                worksheet.Cell(row, 3).Value = _customers.FirstOrDefault(c => c.Id == invoice.CustomerId)?.Name;
                worksheet.Cell(row, 4).Value = invoice.TotalAmount;
                worksheet.Cell(row, 5).Value = invoice.Status;
                row++;
            }

            workbook.SaveAs(filePath);
        }

        public void ExportProductsReportToExcel(List<SimpleProduct> products, string filePath)
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("تقرير المنتجات");

            // إضافة العناوين
            worksheet.Cell(1, 1).Value = "الرمز";
            worksheet.Cell(1, 2).Value = "الاسم";
            worksheet.Cell(1, 3).Value = "الفئة";
            worksheet.Cell(1, 4).Value = "السعر";
            worksheet.Cell(1, 5).Value = "الكمية";
            worksheet.Cell(1, 6).Value = "الحد الأدنى";

            // إضافة البيانات
            int row = 2;
            foreach (var product in products)
            {
                worksheet.Cell(row, 1).Value = product.Code;
                worksheet.Cell(row, 2).Value = product.Name;
                worksheet.Cell(row, 3).Value = _categories.FirstOrDefault(c => c.Id == product.CategoryId)?.Name;
                worksheet.Cell(row, 4).Value = product.Price;
                worksheet.Cell(row, 5).Value = product.Quantity;
                worksheet.Cell(row, 6).Value = product.MinQuantity;
                row++;
            }

            workbook.SaveAs(filePath);
        }

        public void ExportCustomersReportToExcel(List<SimpleCustomer> customers, string filePath)
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("تقرير العملاء");

            // إضافة العناوين
            worksheet.Cell(1, 1).Value = "الاسم";
            worksheet.Cell(1, 2).Value = "الهاتف";
            worksheet.Cell(1, 3).Value = "البريد الإلكتروني";
            worksheet.Cell(1, 4).Value = "العنوان";
            worksheet.Cell(1, 5).Value = "الرصيد";

            // إضافة البيانات
            int row = 2;
            foreach (var customer in customers)
            {
                worksheet.Cell(row, 1).Value = customer.Name;
                worksheet.Cell(row, 2).Value = customer.Phone;
                worksheet.Cell(row, 3).Value = customer.Email;
                worksheet.Cell(row, 4).Value = customer.Address;
                worksheet.Cell(row, 5).Value = customer.Balance;
                row++;
            }

            workbook.SaveAs(filePath);
        }

        public void ExportSuppliersReportToExcel(List<SimpleSupplier> suppliers, string filePath)
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("تقرير الموردين");

            // إضافة العناوين
            worksheet.Cell(1, 1).Value = "الاسم";
            worksheet.Cell(1, 2).Value = "الهاتف";
            worksheet.Cell(1, 3).Value = "البريد الإلكتروني";
            worksheet.Cell(1, 4).Value = "العنوان";
            worksheet.Cell(1, 5).Value = "الرصيد";

            // إضافة البيانات
            int row = 2;
            foreach (var supplier in suppliers)
            {
                worksheet.Cell(row, 1).Value = supplier.Name;
                worksheet.Cell(row, 2).Value = supplier.Phone;
                worksheet.Cell(row, 3).Value = supplier.Email;
                worksheet.Cell(row, 4).Value = supplier.Address;
                worksheet.Cell(row, 5).Value = supplier.Balance;
                row++;
            }

            workbook.SaveAs(filePath);
        }

        public List<SimpleCategory> GetAllCategories()
        {
            return _categories ?? new List<SimpleCategory>();
        }

        public void ExportProductsToExcel(DateTime from, DateTime to, string filePath)
        {
            var products = GetProductsReport(from, to);
            ExportProductsReportToExcel(products, filePath);
        }

        public void ExportCustomersToExcel(DateTime from, DateTime to, string filePath)
        {
            var customers = GetCustomersReport(from, to);
            ExportCustomersReportToExcel(customers, filePath);
        }

        public void ExportSuppliersToExcel(DateTime from, DateTime to, string filePath)
        {
            var suppliers = GetSuppliersReport(from, to);
            ExportSuppliersReportToExcel(suppliers, filePath);
        }
    }
} 