using System;

namespace SimpleAccounting.Models
{
    public class SimpleInvoiceItem
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Discount { get; set; }
        public decimal Total { get; set; }
        public DateTime CreatedAt { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }

        public virtual SimpleInvoice Invoice { get; set; }
        public virtual SimpleProduct Product { get; set; }
    }
} 