using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Services;
using SimpleAccounting.Controls;
using FontAwesome.Sharp;

namespace SimpleAccounting.Forms
{
    public partial class MainForm : Form
    {
        private Panel panelNavigation;
        private Panel panelContent;
        private Panel panelTop;
        private Label lblCurrentUser;
        private Button currentButton;
        private UserControl? currentControl;
        private readonly Color activeButtonColor = Color.FromArgb(0, 122, 204);
        private readonly Color hoverButtonColor = Color.FromArgb(60, 60, 63);
        private readonly Color normalButtonColor = Color.Transparent;

        public MainForm()
        {
            try
            {
                InitializeComponent();
                SetupForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة النموذج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            this.panelNavigation = new Panel();
            this.panelContent = new Panel();
            this.panelTop = new Panel();
            this.lblCurrentUser = new Label();

            // Form settings
            this.Text = "نظام المحاسبة";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.White;

            // Top Panel
            this.panelTop.Dock = DockStyle.Top;
            this.panelTop.Height = 60;
            this.panelTop.BackColor = Color.FromArgb(0, 122, 204);

            this.lblCurrentUser.Text = $"مرحباً، {CurrentUser.DisplayName}";
            this.lblCurrentUser.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.lblCurrentUser.ForeColor = Color.White;
            this.lblCurrentUser.TextAlign = ContentAlignment.MiddleRight;
            this.lblCurrentUser.Dock = DockStyle.Right;
            this.lblCurrentUser.Padding = new Padding(20, 0, 20, 0);

            this.panelTop.Controls.Add(this.lblCurrentUser);

            // Navigation Panel
            this.panelNavigation.Dock = DockStyle.Right;
            this.panelNavigation.Width = 250;
            this.panelNavigation.BackColor = Color.FromArgb(45, 45, 48);
            this.panelNavigation.Padding = new Padding(10);

            // Content Panel
            this.panelContent.Dock = DockStyle.Fill;
            this.panelContent.BackColor = Color.White;
            this.panelContent.Padding = new Padding(20);

            // Add panels to form
            this.Controls.AddRange(new Control[] {
                this.panelTop,
                this.panelNavigation,
                this.panelContent
            });

            // Create navigation buttons
            CreateNavButton("لوحة التحكم", IconChar.Home, 0);
            CreateNavButton("المبيعات", IconChar.ShoppingCart, 1);
            CreateNavButton("المخزون", IconChar.Box, 2);
            CreateNavButton("العملاء", IconChar.Users, 3);
            CreateNavButton("الموردين", IconChar.Truck, 4);
            CreateNavButton("التقارير", IconChar.ChartBar, 5);
            CreateNavButton("الإعدادات", IconChar.Cog, 6);
            CreateNavButton("تسجيل الخروج", IconChar.SignOutAlt, 7);

            // Load default control
            ActivateButton(panelNavigation.Controls[0] as Button);
            LoadUserControl(new DashboardUserControl());
        }

        private void SetupForm()
        {
            // Add any additional setup here
        }

        private void CreateNavButton(string text, IconChar icon, int index)
        {
            try
            {
                var button = new Button
                {
                    Text = $"  {text}",
                    Tag = index,
                    Height = 50,
                    Dock = DockStyle.Top,
                    FlatStyle = FlatStyle.Flat,
                    FlatAppearance = { BorderSize = 0 },
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 12),
                    TextAlign = ContentAlignment.MiddleRight,
                    Image = icon.ToBitmap(Color.White, 24),
                    ImageAlign = ContentAlignment.MiddleLeft,
                    Padding = new Padding(10, 0, 10, 0),
                    Cursor = Cursors.Hand,
                    BackColor = normalButtonColor
                };

                button.Click += NavButton_Click;
                button.MouseEnter += (s, e) => 
                { 
                    if (s != currentButton) 
                        (s as Button).BackColor = hoverButtonColor; 
                };
                button.MouseLeave += (s, e) => 
                { 
                    if (s != currentButton) 
                        (s as Button).BackColor = normalButtonColor; 
                };

                panelNavigation.Controls.Add(button);
                button.BringToFront();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء زر التنقل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void NavButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (sender is Button button)
                {
                    ActivateButton(button);

                    // Load corresponding user control
                    switch (button.Tag)
                    {
                        case 0: // Dashboard
                            LoadUserControl(new DashboardUserControl());
                            break;
                        case 1: // Sales
                            LoadUserControl(new SalesUserControl());
                            break;
                        case 2: // Inventory
                            LoadUserControl(new ProductsUserControl());
                            break;
                        case 3: // Customers
                            LoadUserControl(new CustomersUserControl());
                            break;
                        case 4: // Suppliers
                            LoadUserControl(new SuppliersUserControl());
                            break;
                        case 5: // Reports
                            LoadUserControl(new ReportsUserControl());
                            break;
                        case 6: // Settings
                            LoadUserControl(new SettingsUserControl());
                            break;
                        case 7: // Logout
                            if (MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد",
                                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                            {
                                CurrentUser.Logout();
                                this.Close();
                            }
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الصفحة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ActivateButton(Button? button)
        {
            if (button == null) return;

            if (currentButton != null)
            {
                currentButton.BackColor = normalButtonColor;
                currentButton.ForeColor = Color.White;
            }

            currentButton = button;
            currentButton.BackColor = activeButtonColor;
            currentButton.ForeColor = Color.White;
        }

        private void LoadUserControl(UserControl control)
        {
            try
            {
                if (currentControl != null)
                {
                    panelContent.Controls.Remove(currentControl);
                    currentControl.Dispose();
                }

                currentControl = control;
                currentControl.Dock = DockStyle.Fill;
                panelContent.Controls.Add(currentControl);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل عنصر التحكم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
} 