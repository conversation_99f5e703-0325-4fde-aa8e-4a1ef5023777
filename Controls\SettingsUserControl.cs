using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Controls
{
    public partial class SettingsUserControl : UserControl
    {
        private readonly SimpleDataManager dataManager;
        private Label lblTitle;
        private TabControl tabControl;
        private TabPage tabGeneral;
        private TabPage tabCompany;
        private TabPage tabBackup;
        private TabPage tabUsers;
        private TabPage tabAbout;

        // General Settings
        private Label lblLanguage;
        private ComboBox cmbLanguage;
        private Label lblTheme;
        private ComboBox cmbTheme;
        private Label lblCurrency;
        private TextBox txtCurrency;
        private Label lblDateFormat;
        private TextBox txtDateFormat;
        private Label lblTimeFormat;
        private TextBox txtTimeFormat;

        // Company Settings
        private Label lblCompanyName;
        private TextBox txtCompanyName;
        private Label lblCompanyPhone;
        private TextBox txtCompanyPhone;
        private Label lblCompanyEmail;
        private TextBox txtCompanyEmail;
        private Label lblCompanyAddress;
        private TextBox txtCompanyAddress;
        private Label lblCompanyVAT;
        private TextBox txtCompanyVAT;
        private Label lblCompanyLogo;
        private PictureBox picCompanyLogo;
        private Button btnSelectLogo;
        private Button btnRemoveLogo;
        private TextBox txtCompanyWebsite;
        private TextBox txtTaxRate;
        private Button btnSaveCompany;

        // Backup Settings
        private Label lblBackupPath;
        private TextBox txtBackupPath;
        private Button btnSelectBackupPath;
        private Label lblAutoBackup;
        private CheckBox chkAutoBackup;
        private Label lblBackupInterval;
        private NumericUpDown numBackupInterval;
        private Label lblBackupTime;
        private DateTimePicker dtpBackupTime;
        private Button btnBackupNow;
        private Button btnRestore;

        // Users Settings
        private DataGridView gridUsers;
        private Button btnAddUser;
        private Button btnEditUser;
        private Button btnDeleteUser;
        private Button btnChangePassword;

        // About
        private Label lblVersion;
        private Label lblCopyright;
        private LinkLabel lnkWebsite;

        // Save Button
        private Button btnSave;

        public SettingsUserControl()
        {
            this.dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.tabControl = new TabControl();
            this.tabGeneral = new TabPage();
            this.tabCompany = new TabPage();
            this.tabBackup = new TabPage();
            this.tabUsers = new TabPage();
            this.tabAbout = new TabPage();

            // Title
            this.lblTitle.Text = "إعدادات النظام";
            this.lblTitle.Font = new Font("Segoe UI", 24, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTitle.Dock = DockStyle.Top;
            this.lblTitle.Height = 60;
            this.lblTitle.TextAlign = ContentAlignment.MiddleRight;
            this.lblTitle.Padding = new Padding(0, 0, 20, 0);

            // Tab Control
            this.tabControl.Dock = DockStyle.Fill;
            this.tabControl.Font = new Font("Segoe UI", 12);
            this.tabControl.Padding = new Point(20, 10);
            this.tabControl.Appearance = TabAppearance.FlatButtons;
            this.tabControl.ItemSize = new Size(150, 40);
            this.tabControl.SizeMode = TabSizeMode.Fixed;

            // General Tab
            this.tabGeneral.Text = "عام";
            this.tabGeneral.Padding = new Padding(20);
            this.tabGeneral.UseVisualStyleBackColor = true;

            // Language
            this.lblLanguage = new Label
            {
                Text = "اللغة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 20),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.cmbLanguage = new ComboBox
            {
                Location = new Point(190, 20),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Items = { "العربية", "English" }
            };

            // Theme
            this.lblTheme = new Label
            {
                Text = "المظهر",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 70),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.cmbTheme = new ComboBox
            {
                Location = new Point(190, 70),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Items = { "فاتح", "داكن" }
            };

            // Currency
            this.lblCurrency = new Label
            {
                Text = "العملة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 120),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtCurrency = new TextBox
            {
                Location = new Point(190, 120),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Date Format
            this.lblDateFormat = new Label
            {
                Text = "تنسيق التاريخ",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 170),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtDateFormat = new TextBox
            {
                Location = new Point(190, 170),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Time Format
            this.lblTimeFormat = new Label
            {
                Text = "تنسيق الوقت",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 220),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtTimeFormat = new TextBox
            {
                Location = new Point(190, 220),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Add controls to General tab
            this.tabGeneral.Controls.AddRange(new Control[] {
                this.lblLanguage,
                this.cmbLanguage,
                this.lblTheme,
                this.cmbTheme,
                this.lblCurrency,
                this.txtCurrency,
                this.lblDateFormat,
                this.txtDateFormat,
                this.lblTimeFormat,
                this.txtTimeFormat
            });

            // Company Tab
            this.tabCompany.Text = "الشركة";
            this.tabCompany.Padding = new Padding(20);
            this.tabCompany.UseVisualStyleBackColor = true;

            // Company Name
            this.lblCompanyName = new Label
            {
                Text = "اسم الشركة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 20),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtCompanyName = new TextBox
            {
                Location = new Point(190, 20),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Company Phone
            this.lblCompanyPhone = new Label
            {
                Text = "هاتف الشركة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 70),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtCompanyPhone = new TextBox
            {
                Location = new Point(190, 70),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Company Email
            this.lblCompanyEmail = new Label
            {
                Text = "بريد الشركة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 120),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtCompanyEmail = new TextBox
            {
                Location = new Point(190, 120),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Company Address
            this.lblCompanyAddress = new Label
            {
                Text = "عنوان الشركة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 170),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtCompanyAddress = new TextBox
            {
                Location = new Point(190, 170),
                Size = new Size(300, 60),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle,
                Multiline = true
            };

            // Company VAT
            this.lblCompanyVAT = new Label
            {
                Text = "الرقم الضريبي",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 250),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtCompanyVAT = new TextBox
            {
                Location = new Point(190, 250),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Company Logo
            this.lblCompanyLogo = new Label
            {
                Text = "شعار الشركة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 300),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.picCompanyLogo = new PictureBox
            {
                Location = new Point(190, 300),
                Size = new Size(100, 100),
                SizeMode = PictureBoxSizeMode.Zoom,
                BorderStyle = BorderStyle.FixedSingle
            };

            this.btnSelectLogo = new Button
            {
                Text = "اختيار شعار",
                Location = new Point(300, 300),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnSelectLogo.Click += BtnSelectLogo_Click;

            this.btnRemoveLogo = new Button
            {
                Text = "إزالة الشعار",
                Location = new Point(300, 340),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnRemoveLogo.Click += BtnRemoveLogo_Click;

            // Add controls to Company tab
            this.tabCompany.Controls.AddRange(new Control[] {
                this.lblCompanyName,
                this.txtCompanyName,
                this.lblCompanyPhone,
                this.txtCompanyPhone,
                this.lblCompanyEmail,
                this.txtCompanyEmail,
                this.lblCompanyAddress,
                this.txtCompanyAddress,
                this.lblCompanyVAT,
                this.txtCompanyVAT,
                this.lblCompanyLogo,
                this.picCompanyLogo,
                this.btnSelectLogo,
                this.btnRemoveLogo
            });

            // Backup Tab
            this.tabBackup.Text = "النسخ الاحتياطي";
            this.tabBackup.Padding = new Padding(20);
            this.tabBackup.UseVisualStyleBackColor = true;

            // Backup Path
            this.lblBackupPath = new Label
            {
                Text = "مسار النسخ الاحتياطي",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 20),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtBackupPath = new TextBox
            {
                Location = new Point(190, 20),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle,
                ReadOnly = true
            };

            this.btnSelectBackupPath = new Button
            {
                Text = "اختيار مسار",
                Location = new Point(500, 20),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnSelectBackupPath.Click += BtnSelectBackupPath_Click;

            // Auto Backup
            this.lblAutoBackup = new Label
            {
                Text = "نسخ احتياطي تلقائي",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 70),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.chkAutoBackup = new CheckBox
            {
                Location = new Point(190, 70),
                Size = new Size(30, 30),
                Font = new Font("Segoe UI", 12)
            };

            // Backup Interval
            this.lblBackupInterval = new Label
            {
                Text = "فترة النسخ الاحتياطي (بالأيام)",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 120),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.numBackupInterval = new NumericUpDown
            {
                Location = new Point(190, 120),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                Minimum = 1,
                Maximum = 30,
                Value = 7
            };

            // Backup Time
            this.lblBackupTime = new Label
            {
                Text = "وقت النسخ الاحتياطي",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 170),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.dtpBackupTime = new DateTimePicker
            {
                Location = new Point(190, 170),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                Format = DateTimePickerFormat.Time,
                ShowUpDown = true
            };

            // Backup Buttons
            this.btnBackupNow = new Button
            {
                Text = "نسخ احتياطي الآن",
                Location = new Point(190, 220),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnBackupNow.Click += BtnBackupNow_Click;

            this.btnRestore = new Button
            {
                Text = "استعادة نسخة احتياطية",
                Location = new Point(350, 220),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnRestore.Click += BtnRestore_Click;

            // Add controls to Backup tab
            this.tabBackup.Controls.AddRange(new Control[] {
                this.lblBackupPath,
                this.txtBackupPath,
                this.btnSelectBackupPath,
                this.lblAutoBackup,
                this.chkAutoBackup,
                this.lblBackupInterval,
                this.numBackupInterval,
                this.lblBackupTime,
                this.dtpBackupTime,
                this.btnBackupNow,
                this.btnRestore
            });

            // Users Tab
            this.tabUsers.Text = "المستخدمين";
            this.tabUsers.Padding = new Padding(20);
            this.tabUsers.UseVisualStyleBackColor = true;

            // Users Grid
            this.gridUsers = new DataGridView
            {
                Location = new Point(20, 20),
                Size = new Size(760, 300),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 12),
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    BackColor = Color.FromArgb(240, 240, 240),
                    ForeColor = Color.FromArgb(64, 64, 64)
                },
                ColumnHeadersHeight = 40,
                RowTemplate = { Height = 40 },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    SelectionBackColor = Color.FromArgb(0, 123, 255),
                    SelectionForeColor = Color.White
                }
            };

            // User Buttons
            this.btnAddUser = new Button
            {
                Text = "إضافة مستخدم",
                Location = new Point(20, 340),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnAddUser.Click += BtnAddUser_Click;

            this.btnEditUser = new Button
            {
                Text = "تعديل",
                Location = new Point(190, 340),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnEditUser.Click += BtnEditUser_Click;

            this.btnDeleteUser = new Button
            {
                Text = "حذف",
                Location = new Point(360, 340),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnDeleteUser.Click += BtnDeleteUser_Click;

            this.btnChangePassword = new Button
            {
                Text = "تغيير كلمة المرور",
                Location = new Point(530, 340),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnChangePassword.Click += BtnChangePassword_Click;

            // Add controls to Users tab
            this.tabUsers.Controls.AddRange(new Control[] {
                this.gridUsers,
                this.btnAddUser,
                this.btnEditUser,
                this.btnDeleteUser,
                this.btnChangePassword
            });

            // Add tabs to tab control
            this.tabControl.TabPages.AddRange(new TabPage[] {
                this.tabGeneral,
                this.tabCompany,
                this.tabBackup,
                this.tabUsers
            });

            // Save Button
            this.btnSave = new Button
            {
                Text = "حفظ الإعدادات",
                Dock = DockStyle.Bottom,
                Height = 50,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnSave.Click += BtnSave_Click;

            // About Tab
            this.tabAbout.Text = "حول البرنامج";
            this.tabAbout.Padding = new Padding(20);
            this.tabAbout.UseVisualStyleBackColor = true;

            // Version
            this.lblVersion = new Label
            {
                Text = "الإصدار: 1.0.0",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 20),
                AutoSize = true
            };

            this.lblCopyright = new Label
            {
                Text = "جميع الحقوق محفوظة © 2024",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 60),
                AutoSize = true
            };

            this.lnkWebsite = new LinkLabel
            {
                Text = "www.example.com",
                Location = new Point(20, 100),
                AutoSize = true,
                LinkArea = new LinkArea(0, 12)
            };
            this.lnkWebsite.LinkClicked += LnkWebsite_LinkClicked;

            this.tabAbout.Controls.Add(this.lblVersion);
            this.tabAbout.Controls.Add(this.lblCopyright);
            this.tabAbout.Controls.Add(this.lnkWebsite);

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.tabControl,
                this.btnSave
            });
        }

        private void LoadData()
        {
            var settings = dataManager.GetSettings();

            // General Settings
            this.cmbLanguage.SelectedItem = settings.Language;
            this.cmbTheme.SelectedItem = settings.Theme;
            this.txtCurrency.Text = settings.Currency;
            this.txtDateFormat.Text = settings.DateFormat;
            this.txtTimeFormat.Text = settings.TimeFormat;

            // Company Settings
            this.txtCompanyName.Text = settings.CompanyName;
            this.txtCompanyPhone.Text = settings.CompanyPhone;
            this.txtCompanyEmail.Text = settings.CompanyEmail;
            this.txtCompanyAddress.Text = settings.CompanyAddress;
            this.txtCompanyVAT.Text = settings.CompanyVAT;
            this.txtCompanyWebsite.Text = settings.CompanyWebsite;
            this.txtTaxRate.Text = settings.TaxRate.ToString();
            if (!string.IsNullOrEmpty(settings.CompanyLogo))
            {
                this.picCompanyLogo.Image = Image.FromFile(settings.CompanyLogo);
            }

            // Backup Settings
            this.txtBackupPath.Text = settings.BackupPath;
            this.chkAutoBackup.Checked = settings.AutoBackup;
            this.numBackupInterval.Value = settings.BackupInterval;
            this.dtpBackupTime.Value = DateTime.Today.Add(settings.BackupTime);

            // Users
            var users = dataManager.GetAllUsers();
            this.gridUsers.DataSource = users;
            this.gridUsers.Columns["Id"].Visible = false;
            this.gridUsers.Columns["Username"].HeaderText = "اسم المستخدم";
            this.gridUsers.Columns["FullName"].HeaderText = "الاسم الكامل";
            this.gridUsers.Columns["Role"].HeaderText = "الدور";
            this.gridUsers.Columns["IsActive"].HeaderText = "نشط";
        }

        private void BtnSelectLogo_Click(object sender, EventArgs e)
        {
            using (var dialog = new OpenFileDialog())
            {
                dialog.Filter = "صور|*.jpg;*.jpeg;*.png;*.gif;*.bmp";
                dialog.Title = "اختيار شعار الشركة";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    this.picCompanyLogo.Image = Image.FromFile(dialog.FileName);
                }
            }
        }

        private void BtnRemoveLogo_Click(object sender, EventArgs e)
        {
            this.picCompanyLogo.Image = null;
        }

        private void BtnSelectBackupPath_Click(object sender, EventArgs e)
        {
            using (var dialog = new FolderBrowserDialog())
            {
                dialog.Description = "اختيار مسار النسخ الاحتياطي";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    this.txtBackupPath.Text = dialog.SelectedPath;
                }
            }
        }

        private void BtnBackupNow_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(this.txtBackupPath.Text))
            {
                MessageBox.Show("الرجاء اختيار مسار النسخ الاحتياطي", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                dataManager.BackupDatabase(this.txtBackupPath.Text);
                MessageBox.Show("تم النسخ الاحتياطي بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء النسخ الاحتياطي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRestore_Click(object sender, EventArgs e)
        {
            using (var dialog = new OpenFileDialog())
            {
                dialog.Filter = "ملفات قاعدة البيانات|*.db";
                dialog.Title = "اختيار ملف النسخة الاحتياطية";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    var result = MessageBox.Show(
                        "سيتم إغلاق التطبيق واستعادة النسخة الاحتياطية. هل تريد المتابعة؟",
                        "تأكيد الاستعادة",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        try
                        {
                            dataManager.RestoreDatabase(dialog.FileName);
                            Application.Exit();
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
        }

        private void BtnAddUser_Click(object sender, EventArgs e)
        {
            using (var form = new UserDetailsForm())
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void BtnEditUser_Click(object sender, EventArgs e)
        {
            if (gridUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار مستخدم للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var user = (SimpleUser)gridUsers.SelectedRows[0].DataBoundItem;
            using (var form = new UserDetailsForm(user))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void BtnDeleteUser_Click(object sender, EventArgs e)
        {
            if (gridUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار مستخدم للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var user = (SimpleUser)gridUsers.SelectedRows[0].DataBoundItem;
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المستخدم {user.Username}؟",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                dataManager.DeleteUser(user.Id);
                LoadData();
            }
        }

        private void BtnChangePassword_Click(object sender, EventArgs e)
        {
            if (gridUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار مستخدم لتغيير كلمة المرور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var user = (SimpleUser)gridUsers.SelectedRows[0].DataBoundItem;
            using (var form = new ChangePasswordForm(user))
            {
                form.ShowDialog();
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            var settings = new SimpleSettings
            {
                Language = cmbLanguage.SelectedItem.ToString(),
                Theme = cmbTheme.SelectedItem.ToString(),
                Currency = txtCurrency.Text,
                DateFormat = txtDateFormat.Text,
                TimeFormat = txtTimeFormat.Text,
                CompanyName = txtCompanyName.Text,
                CompanyPhone = txtCompanyPhone.Text,
                CompanyEmail = txtCompanyEmail.Text,
                CompanyAddress = txtCompanyAddress.Text,
                CompanyVAT = txtCompanyVAT.Text,
                CompanyWebsite = txtCompanyWebsite.Text,
                TaxRate = decimal.Parse(txtTaxRate.Text),
                CompanyLogo = picCompanyLogo.Image != null ? "logo.png" : null,
                BackupPath = txtBackupPath.Text,
                AutoBackup = chkAutoBackup.Checked,
                BackupInterval = (int)numBackupInterval.Value,
                BackupTime = dtpBackupTime.Value.TimeOfDay
            };

            dataManager.UpdateSettings(settings);

            if (picCompanyLogo.Image != null)
            {
                picCompanyLogo.Image.Save("logo.png");
            }

            MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LnkWebsite_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
            {
                FileName = "https://www.example.com",
                UseShellExecute = true
            });
        }
    }
} 