using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Forms
{
    public partial class SupplierPaymentsForm : Form
    {
        private readonly SimpleDataManager dataManager;
        private readonly SimpleSupplier supplier;

        private Label lblTitle;
        private Label lblSupplierInfo;
        private Label lblCurrentBalance;
        private Label lblCurrentBalanceValue;
        private Label lblPaymentAmount;
        private NumericUpDown numPaymentAmount;
        private Label lblPaymentType;
        private ComboBox cmbPaymentType;
        private Label lblNotes;
        private TextBox txtNotes;
        private Button btnSave;
        private Button btnCancel;

        public SupplierPaymentsForm(SimpleSupplier supplier)
        {
            this.dataManager = new SimpleDataManager();
            this.supplier = supplier;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.lblSupplierInfo = new Label();
            this.lblCurrentBalance = new Label();
            this.lblCurrentBalanceValue = new Label();
            this.lblPaymentAmount = new Label();
            this.numPaymentAmount = new NumericUpDown();
            this.lblPaymentType = new Label();
            this.cmbPaymentType = new ComboBox();
            this.lblNotes = new Label();
            this.txtNotes = new TextBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();

            // Form
            this.Text = "مدفوعات المورد";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Title
            this.lblTitle.Text = "مدفوعات المورد";
            this.lblTitle.Font = new Font("Segoe UI", 24, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTitle.Dock = DockStyle.Top;
            this.lblTitle.Height = 60;
            this.lblTitle.TextAlign = ContentAlignment.MiddleRight;
            this.lblTitle.Padding = new Padding(0, 0, 20, 0);

            // Supplier Info
            this.lblSupplierInfo.Font = new Font("Segoe UI", 12);
            this.lblSupplierInfo.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblSupplierInfo.Location = new Point(20, 80);
            this.lblSupplierInfo.Size = new Size(440, 30);
            this.lblSupplierInfo.TextAlign = ContentAlignment.MiddleRight;

            // Current Balance
            this.lblCurrentBalance.Text = "الرصيد الحالي";
            this.lblCurrentBalance.Font = new Font("Segoe UI", 12);
            this.lblCurrentBalance.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblCurrentBalance.Location = new Point(20, 130);
            this.lblCurrentBalance.Size = new Size(100, 30);
            this.lblCurrentBalance.TextAlign = ContentAlignment.MiddleRight;

            this.lblCurrentBalanceValue.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            this.lblCurrentBalanceValue.ForeColor = Color.FromArgb(0, 123, 255);
            this.lblCurrentBalanceValue.Location = new Point(140, 130);
            this.lblCurrentBalanceValue.Size = new Size(300, 30);
            this.lblCurrentBalanceValue.TextAlign = ContentAlignment.MiddleRight;

            // Payment Amount
            this.lblPaymentAmount.Text = "قيمة المدفوعات";
            this.lblPaymentAmount.Font = new Font("Segoe UI", 12);
            this.lblPaymentAmount.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblPaymentAmount.Location = new Point(20, 180);
            this.lblPaymentAmount.Size = new Size(100, 30);
            this.lblPaymentAmount.TextAlign = ContentAlignment.MiddleRight;

            this.numPaymentAmount.Location = new Point(140, 180);
            this.numPaymentAmount.Size = new Size(300, 30);
            this.numPaymentAmount.Font = new Font("Segoe UI", 12);
            this.numPaymentAmount.DecimalPlaces = 2;
            this.numPaymentAmount.Maximum = 999999;
            this.numPaymentAmount.Minimum = 0;
            this.numPaymentAmount.Value = 0;

            // Payment Type
            this.lblPaymentType.Text = "نوع المدفوعات";
            this.lblPaymentType.Font = new Font("Segoe UI", 12);
            this.lblPaymentType.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblPaymentType.Location = new Point(20, 230);
            this.lblPaymentType.Size = new Size(100, 30);
            this.lblPaymentType.TextAlign = ContentAlignment.MiddleRight;

            this.cmbPaymentType.Location = new Point(140, 230);
            this.cmbPaymentType.Size = new Size(300, 30);
            this.cmbPaymentType.Font = new Font("Segoe UI", 12);
            this.cmbPaymentType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPaymentType.Items.AddRange(new object[] { "نقدي", "تحويل بنكي", "شبكة" });

            // Notes
            this.lblNotes.Text = "ملاحظات";
            this.lblNotes.Font = new Font("Segoe UI", 12);
            this.lblNotes.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblNotes.Location = new Point(20, 280);
            this.lblNotes.Size = new Size(100, 30);
            this.lblNotes.TextAlign = ContentAlignment.MiddleRight;

            this.txtNotes.Location = new Point(140, 280);
            this.txtNotes.Size = new Size(300, 60);
            this.txtNotes.Font = new Font("Segoe UI", 12);
            this.txtNotes.BorderStyle = BorderStyle.FixedSingle;
            this.txtNotes.Multiline = true;

            // Buttons
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(140, 360);
            this.btnSave.Size = new Size(150, 40);
            this.btnSave.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnSave.BackColor = Color.FromArgb(40, 167, 69);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.Cursor = Cursors.Hand;
            this.btnSave.Click += BtnSave_Click;

            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(300, 360);
            this.btnCancel.Size = new Size(150, 40);
            this.btnCancel.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.Cursor = Cursors.Hand;
            this.btnCancel.Click += BtnCancel_Click;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.lblSupplierInfo,
                this.lblCurrentBalance,
                this.lblCurrentBalanceValue,
                this.lblPaymentAmount,
                this.numPaymentAmount,
                this.lblPaymentType,
                this.cmbPaymentType,
                this.lblNotes,
                this.txtNotes,
                this.btnSave,
                this.btnCancel
            });
        }

        private void LoadData()
        {
            this.lblSupplierInfo.Text = $"المورد: {supplier.Name}";
            this.lblCurrentBalanceValue.Text = supplier.Balance.ToString("N2");
            this.cmbPaymentType.SelectedIndex = 0;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (numPaymentAmount.Value <= 0)
            {
                MessageBox.Show("الرجاء إدخال قيمة المدفوعات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numPaymentAmount.Focus();
                return;
            }

            if (cmbPaymentType.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار نوع المدفوعات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbPaymentType.Focus();
                return;
            }

            var payment = new SimplePayment
            {
                SupplierId = supplier.Id,
                Amount = (decimal)numPaymentAmount.Value,
                PaymentType = cmbPaymentType.SelectedItem.ToString(),
                Notes = txtNotes.Text,
                Date = DateTime.Now
            };

            dataManager.AddPayment(payment);
            dataManager.UpdateSupplierBalance(supplier.Id, -payment.Amount);

            DialogResult = DialogResult.OK;
            Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
} 