using System;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Forms
{
    public partial class InvoiceDetailsForm : Form
    {
        public SimpleInvoice Invoice { get; set; }
        private SimpleDataManager dataManager;
        private readonly bool isEditMode;

        private Label lblTitle;
        private Label lblCustomer;
        private ComboBox cmbCustomer;
        private Label lblDate;
        private DateTimePicker dtpDate;
        private Label lblNumber;
        private TextBox txtNumber;
        private Label lblNotes;
        private TextBox txtNotes;

        private DataGridView gridItems;
        private Button btnAddItem;
        private Button btnEditItem;
        private Button btnDeleteItem;

        private Label lblTotalAmount;
        private NumericUpDown numTotalAmount;
        private Label lblPaidAmount;
        private NumericUpDown numPaidAmount;
        private Label lblRemainingAmount;
        private NumericUpDown numRemainingAmount;
        private Label lblStatus;
        private ComboBox cmbStatus;

        private Button btnSave;
        private Button btnCancel;

        public InvoiceDetailsForm(SimpleInvoice invoice, SimpleDataManager dataManager)
        {
            this.Invoice = invoice;
            this.dataManager = dataManager;
            this.isEditMode = invoice != null;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = isEditMode ? "تعديل فاتورة" : "فاتورة جديدة";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 12);

            // Title
            this.lblTitle = new Label
            {
                Text = isEditMode ? "تعديل فاتورة" : "فاتورة جديدة",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                Dock = DockStyle.Top,
                Height = 60,
                TextAlign = ContentAlignment.MiddleRight,
                Padding = new Padding(0, 0, 20, 0)
            };

            // Customer
            this.lblCustomer = new Label
            {
                Text = "العميل",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 80),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.cmbCustomer = new ComboBox
            {
                Location = new Point(190, 80),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Date
            this.lblDate = new Label
            {
                Text = "التاريخ",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(510, 80),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.dtpDate = new DateTimePicker
            {
                Location = new Point(680, 80),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                Format = DateTimePickerFormat.Short
            };

            // Number
            this.lblNumber = new Label
            {
                Text = "رقم الفاتورة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 130),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtNumber = new TextBox
            {
                Location = new Point(190, 130),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Notes
            this.lblNotes = new Label
            {
                Text = "ملاحظات",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(510, 130),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtNotes = new TextBox
            {
                Location = new Point(680, 130),
                Size = new Size(200, 60),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle,
                Multiline = true
            };

            // Items Grid
            this.gridItems = new DataGridView
            {
                Location = new Point(20, 200),
                Size = new Size(860, 200),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 12),
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    BackColor = Color.FromArgb(240, 240, 240),
                    ForeColor = Color.FromArgb(64, 64, 64)
                },
                ColumnHeadersHeight = 40,
                RowTemplate = { Height = 40 },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    SelectionBackColor = Color.FromArgb(0, 123, 255),
                    SelectionForeColor = Color.White
                }
            };

            // Item Buttons
            this.btnAddItem = new Button
            {
                Text = "إضافة منتج",
                Location = new Point(20, 420),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnAddItem.Click += BtnAddItem_Click;

            this.btnEditItem = new Button
            {
                Text = "تعديل",
                Location = new Point(190, 420),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnEditItem.Click += BtnEditItem_Click;

            this.btnDeleteItem = new Button
            {
                Text = "حذف",
                Location = new Point(360, 420),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnDeleteItem.Click += BtnDeleteItem_Click;

            // Total Amount
            this.lblTotalAmount = new Label
            {
                Text = "المبلغ الإجمالي",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 480),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.numTotalAmount = new NumericUpDown
            {
                Location = new Point(190, 480),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                DecimalPlaces = 2,
                Maximum = 999999,
                ReadOnly = true
            };

            // Paid Amount
            this.lblPaidAmount = new Label
            {
                Text = "المبلغ المدفوع",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(410, 480),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.numPaidAmount = new NumericUpDown
            {
                Location = new Point(580, 480),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                DecimalPlaces = 2,
                Maximum = 999999
            };
            this.numPaidAmount.ValueChanged += NumPaidAmount_ValueChanged;

            // Remaining Amount
            this.lblRemainingAmount = new Label
            {
                Text = "المبلغ المتبقي",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 530),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.numRemainingAmount = new NumericUpDown
            {
                Location = new Point(190, 530),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                DecimalPlaces = 2,
                Maximum = 999999,
                ReadOnly = true
            };

            // Status
            this.lblStatus = new Label
            {
                Text = "الحالة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(410, 530),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.cmbStatus = new ComboBox
            {
                Location = new Point(580, 530),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Items = { "مدفوعة", "مدفوعة جزئياً", "غير مدفوعة" }
            };

            // Save Button
            this.btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(580, 580),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnSave.Click += BtnSave_Click;

            // Cancel Button
            this.btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(410, 580),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnCancel.Click += BtnCancel_Click;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.lblCustomer,
                this.cmbCustomer,
                this.lblDate,
                this.dtpDate,
                this.lblNumber,
                this.txtNumber,
                this.lblNotes,
                this.txtNotes,
                this.gridItems,
                this.btnAddItem,
                this.btnEditItem,
                this.btnDeleteItem,
                this.lblTotalAmount,
                this.numTotalAmount,
                this.lblPaidAmount,
                this.numPaidAmount,
                this.lblRemainingAmount,
                this.numRemainingAmount,
                this.lblStatus,
                this.cmbStatus,
                this.btnSave,
                this.btnCancel
            });
        }

        private void LoadData()
        {
            // Load customers
            var customers = dataManager.GetAllCustomers();
            this.cmbCustomer.Items.Clear();
            foreach (var customer in customers)
            {
                this.cmbCustomer.Items.Add(customer);
            }
            this.cmbCustomer.DisplayMember = "Name";

            if (isEditMode)
            {
                this.cmbCustomer.SelectedItem = customers.FirstOrDefault(c => c.Id == Invoice.CustomerId);
                this.dtpDate.Value = Invoice.Date;
                this.txtNumber.Text = Invoice.Number;
                this.txtNotes.Text = Invoice.Notes;
                this.numTotalAmount.Value = Invoice.TotalAmount;
                this.numPaidAmount.Value = Invoice.PaidAmount;
                this.numRemainingAmount.Value = Invoice.RemainingAmount;
                this.cmbStatus.SelectedItem = Invoice.Status;

                // Load items
                var items = dataManager.GetInvoiceItems(Invoice.Id);
                this.gridItems.DataSource = items;
                this.gridItems.Columns["Id"].Visible = false;
                this.gridItems.Columns["InvoiceId"].Visible = false;
                this.gridItems.Columns["ProductId"].Visible = false;
                this.gridItems.Columns["Product"].HeaderText = "المنتج";
                this.gridItems.Columns["Quantity"].HeaderText = "الكمية";
                this.gridItems.Columns["UnitPrice"].HeaderText = "سعر الوحدة";
                this.gridItems.Columns["Discount"].HeaderText = "الخصم";
                this.gridItems.Columns["Total"].HeaderText = "الإجمالي";
            }
            else
            {
                this.dtpDate.Value = DateTime.Now;
                this.txtNumber.Text = dataManager.GetNextInvoiceNumber();
                this.cmbStatus.SelectedIndex = 0;
            }
        }

        private void NumPaidAmount_ValueChanged(object sender, EventArgs e)
        {
            this.numRemainingAmount.Value = this.numTotalAmount.Value - this.numPaidAmount.Value;
            if (this.numRemainingAmount.Value == 0)
            {
                this.cmbStatus.SelectedItem = "مدفوعة";
            }
            else if (this.numPaidAmount.Value > 0)
            {
                this.cmbStatus.SelectedItem = "مدفوعة جزئياً";
            }
            else
            {
                this.cmbStatus.SelectedItem = "غير مدفوعة";
            }
        }

        private void BtnAddItem_Click(object sender, EventArgs e)
        {
            if (cmbCustomer.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار العميل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using (var form = new InvoiceItemForm())
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    var item = form.InvoiceItem;
                    var items = (System.Collections.Generic.List<SimpleInvoiceItem>)gridItems.DataSource;
                    if (items == null)
                    {
                        items = new System.Collections.Generic.List<SimpleInvoiceItem>();
                    }
                    items.Add(item);
                    this.gridItems.DataSource = null;
                    this.gridItems.DataSource = items;
                    CalculateTotal();
                }
            }
        }

        private void BtnEditItem_Click(object sender, EventArgs e)
        {
            if (gridItems.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار منتج للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var item = (SimpleInvoiceItem)gridItems.SelectedRows[0].DataBoundItem;
            using (var form = new InvoiceItemForm(item))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    var items = (System.Collections.Generic.List<SimpleInvoiceItem>)gridItems.DataSource;
                    var index = items.IndexOf(item);
                    items[index] = form.InvoiceItem;
                    this.gridItems.DataSource = null;
                    this.gridItems.DataSource = items;
                    CalculateTotal();
                }
            }
        }

        private void BtnDeleteItem_Click(object sender, EventArgs e)
        {
            if (gridItems.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار منتج للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var item = (SimpleInvoiceItem)gridItems.SelectedRows[0].DataBoundItem;
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المنتج {item.Product.Name}؟",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                var items = (System.Collections.Generic.List<SimpleInvoiceItem>)gridItems.DataSource;
                items.Remove(item);
                this.gridItems.DataSource = null;
                this.gridItems.DataSource = items;
                CalculateTotal();
            }
        }

        private void CalculateTotal()
        {
            var items = (System.Collections.Generic.List<SimpleInvoiceItem>)gridItems.DataSource;
            if (items != null)
            {
                this.numTotalAmount.Value = items.Sum(i => i.Total);
                this.numRemainingAmount.Value = this.numTotalAmount.Value - this.numPaidAmount.Value;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (cmbCustomer.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار العميل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(txtNumber.Text))
            {
                MessageBox.Show("الرجاء إدخال رقم الفاتورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var items = (System.Collections.Generic.List<SimpleInvoiceItem>)gridItems.DataSource;
            if (items == null || items.Count == 0)
            {
                MessageBox.Show("الرجاء إضافة منتج واحد على الأقل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var customer = (SimpleCustomer)cmbCustomer.SelectedItem;
            var invoice = new SimpleInvoice
            {
                CustomerId = customer.Id,
                Date = dtpDate.Value,
                Number = txtNumber.Text,
                Notes = txtNotes.Text,
                TotalAmount = numTotalAmount.Value,
                PaidAmount = numPaidAmount.Value,
                RemainingAmount = numRemainingAmount.Value,
                Status = cmbStatus.SelectedItem.ToString()
            };

            if (isEditMode)
            {
                invoice.Id = this.Invoice.Id;
                dataManager.UpdateInvoice(invoice);
                dataManager.DeleteInvoiceItems(invoice.Id);
            }
            else
            {
                dataManager.AddInvoice(invoice);
            }

            foreach (var item in items)
            {
                item.InvoiceId = invoice.Id;
                dataManager.AddInvoiceItem(item);
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
} 