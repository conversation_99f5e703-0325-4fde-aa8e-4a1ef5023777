using System;

namespace SimpleAccounting.Models
{
    public class SimpleSettings
    {
        public int Id { get; set; }
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }
        public string CompanyEmail { get; set; }
        public string CompanyAddress { get; set; }
        public string CompanyVAT { get; set; }
        public string Language { get; set; }
        public string Theme { get; set; }
        public string Currency { get; set; }
        public string DateFormat { get; set; }
        public string TimeFormat { get; set; }
        public string BackupPath { get; set; }
        public bool AutoBackup { get; set; }
        public int BackupInterval { get; set; }
        public TimeSpan BackupTime { get; set; }
        public string CompanyWebsite { get; set; }
        public decimal TaxRate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public byte[] CompanyLogo { get; set; }
    }
} 