using System;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;
using SimpleAccounting.Data;
using SimpleAccounting.Forms;
using SimpleAccounting.Models;

namespace SimpleAccounting.Controls
{
    public partial class CustomersUserControl : UserControl
    {
        private readonly SimpleDataManager dataManager;
        private Label lblTitle;
        private Label lblTotalCustomers;
        private Label lblTotalSales;
        private Label lblTotalPayments;
        private TextBox txtSearch;
        private Button btnSearch;
        private DataGridView gridCustomers;
        private Button btnNewCustomer;
        private Button btnEditCustomer;
        private Button btnDeleteCustomer;
        private Button btnExport;

        public CustomersUserControl()
        {
            dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.lblTotalCustomers = new Label();
            this.lblTotalSales = new Label();
            this.lblTotalPayments = new Label();
            this.txtSearch = new TextBox();
            this.btnSearch = new Button();
            this.gridCustomers = new DataGridView();
            this.btnNewCustomer = new Button();
            this.btnEditCustomer = new Button();
            this.btnDeleteCustomer = new Button();
            this.btnExport = new Button();

            // إعداد العنوان
            lblTitle.Text = "العملاء";
            lblTitle.Font = new System.Drawing.Font("Arial", 16, System.Drawing.FontStyle.Bold);
            lblTitle.Location = new System.Drawing.Point(20, 20);
            lblTitle.AutoSize = true;

            // إعداد الإحصائيات
            lblTotalCustomers.Text = "عدد العملاء: 0";
            lblTotalCustomers.Location = new System.Drawing.Point(20, 60);
            lblTotalCustomers.AutoSize = true;

            lblTotalSales.Text = "إجمالي المبيعات: 0";
            lblTotalSales.Location = new System.Drawing.Point(200, 60);
            lblTotalSales.AutoSize = true;

            lblTotalPayments.Text = "إجمالي المدفوعات: 0";
            lblTotalPayments.Location = new System.Drawing.Point(380, 60);
            lblTotalPayments.AutoSize = true;

            // إعداد أدوات البحث
            txtSearch.PlaceholderText = "بحث...";
            txtSearch.Location = new System.Drawing.Point(20, 100);
            txtSearch.Size = new System.Drawing.Size(200, 25);
            txtSearch.TextChanged += TxtSearch_TextChanged;

            btnSearch.Text = "بحث";
            btnSearch.Location = new System.Drawing.Point(240, 100);
            btnSearch.Size = new System.Drawing.Size(100, 25);
            btnSearch.Click += BtnSearch_Click;

            // إعداد جدول العملاء
            gridCustomers.Location = new System.Drawing.Point(20, 140);
            gridCustomers.Size = new System.Drawing.Size(880, 400);
            gridCustomers.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            gridCustomers.ReadOnly = true;
            gridCustomers.AllowUserToAddRows = false;
            gridCustomers.AllowUserToDeleteRows = false;
            gridCustomers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            gridCustomers.MultiSelect = false;
            gridCustomers.CellDoubleClick += GridCustomers_CellDoubleClick;

            // إعداد الأزرار
            btnNewCustomer.Text = "عميل جديد";
            btnNewCustomer.Location = new System.Drawing.Point(20, 560);
            btnNewCustomer.Size = new System.Drawing.Size(120, 30);
            btnNewCustomer.Click += BtnNewCustomer_Click;

            btnEditCustomer.Text = "تعديل";
            btnEditCustomer.Location = new System.Drawing.Point(150, 560);
            btnEditCustomer.Size = new System.Drawing.Size(120, 30);
            btnEditCustomer.Click += BtnEditCustomer_Click;

            btnDeleteCustomer.Text = "حذف";
            btnDeleteCustomer.Location = new System.Drawing.Point(280, 560);
            btnDeleteCustomer.Size = new System.Drawing.Size(120, 30);
            btnDeleteCustomer.Click += BtnDeleteCustomer_Click;

            btnExport.Text = "تصدير";
            btnExport.Location = new System.Drawing.Point(410, 560);
            btnExport.Size = new System.Drawing.Size(120, 30);
            btnExport.Click += BtnExport_Click;

            // إضافة عناصر التحكم
            this.Controls.Add(lblTitle);
            this.Controls.Add(lblTotalCustomers);
            this.Controls.Add(lblTotalSales);
            this.Controls.Add(lblTotalPayments);
            this.Controls.Add(txtSearch);
            this.Controls.Add(btnSearch);
            this.Controls.Add(gridCustomers);
            this.Controls.Add(btnNewCustomer);
            this.Controls.Add(btnEditCustomer);
            this.Controls.Add(btnDeleteCustomer);
            this.Controls.Add(btnExport);
        }

        private void LoadData()
        {
            // تحميل الإحصائيات
            var stats = dataManager.GetDashboardStats();
            lblTotalCustomers.Text = $"عدد العملاء: {stats.TotalCustomers}";
            lblTotalSales.Text = $"إجمالي المبيعات: {stats.TotalSales:N2}";
            lblTotalPayments.Text = $"إجمالي المدفوعات: {stats.TotalPayments:N2}";

            // تحميل العملاء
            LoadCustomers();
        }

        private void LoadCustomers()
        {
            var customers = dataManager.GetCustomers(txtSearch.Text);
            gridCustomers.DataSource = customers;
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            LoadCustomers();
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            LoadCustomers();
        }

        private void GridCustomers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var customer = (SimpleCustomer)gridCustomers.Rows[e.RowIndex].DataBoundItem;
                var form = new CustomerDetailsForm(customer, dataManager);
                form.ShowDialog();
                LoadCustomers();
            }
        }

        private void BtnNewCustomer_Click(object sender, EventArgs e)
        {
            var form = new CustomerDetailsForm(null, dataManager);
            form.ShowDialog();
            LoadCustomers();
        }

        private void BtnEditCustomer_Click(object sender, EventArgs e)
        {
            if (gridCustomers.SelectedRows.Count > 0)
            {
                var customer = (SimpleCustomer)gridCustomers.SelectedRows[0].DataBoundItem;
                var form = new CustomerDetailsForm(customer, dataManager);
                form.ShowDialog();
                LoadCustomers();
            }
        }

        private void BtnDeleteCustomer_Click(object sender, EventArgs e)
        {
            if (gridCustomers.SelectedRows.Count > 0)
            {
                var customer = (SimpleCustomer)gridCustomers.SelectedRows[0].DataBoundItem;
                if (MessageBox.Show("هل أنت متأكد من حذف هذا العميل؟", "تأكيد", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    dataManager.DeleteCustomer(customer.Id);
                    LoadCustomers();
                }
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            var dialog = new SaveFileDialog();
            dialog.Filter = "Excel Files|*.xlsx";
            dialog.Title = "تصدير العملاء";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                dataManager.ExportCustomersToExcel(dialog.FileName);
                MessageBox.Show("تم تصدير العملاء بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }
} 