using System;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Forms
{
    public partial class SalesListForm : Form
    {
        private readonly SimpleDataManager dataManager;
        private TextBox txtSearch;
        private DateTimePicker dtpFrom;
        private DateTimePicker dtpTo;
        private ComboBox cmbCustomer;
        private DataGridView gridSales;
        private Button btnSearch;
        private Button btnPrint;
        private Button btnClose;

        public SalesListForm()
        {
            this.dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "قائمة المبيعات";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Search
            var lblSearch = new Label
            {
                Text = "بحث:",
                Location = new Point(20, 20),
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.txtSearch = new TextBox
            {
                Location = new Point(130, 20),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                PlaceholderText = "رقم الفاتورة أو اسم العميل..."
            };

            // Date Range
            var lblFrom = new Label
            {
                Text = "من:",
                Location = new Point(350, 20),
                Size = new Size(50, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.dtpFrom = new DateTimePicker
            {
                Location = new Point(410, 20),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12),
                Format = DateTimePickerFormat.Short
            };

            var lblTo = new Label
            {
                Text = "إلى:",
                Location = new Point(580, 20),
                Size = new Size(50, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.dtpTo = new DateTimePicker
            {
                Location = new Point(640, 20),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12),
                Format = DateTimePickerFormat.Short
            };

            // Customer Filter
            var lblCustomer = new Label
            {
                Text = "العميل:",
                Location = new Point(20, 60),
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.cmbCustomer = new ComboBox
            {
                Location = new Point(130, 60),
                Size = new Size(300, 30),
                Font = new Font("Segoe UI", 12),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Search Button
            this.btnSearch = new Button
            {
                Text = "بحث",
                Location = new Point(450, 60),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnSearch.FlatAppearance.BorderSize = 0;
            this.btnSearch.Click += BtnSearch_Click;

            // Grid
            this.gridSales = new DataGridView
            {
                Location = new Point(20, 100),
                Size = new Size(940, 400),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 12),
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    BackColor = Color.FromArgb(240, 240, 240),
                    ForeColor = Color.FromArgb(64, 64, 64)
                },
                ColumnHeadersHeight = 40,
                RowTemplate = { Height = 40 },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    SelectionBackColor = Color.FromArgb(0, 123, 255),
                    SelectionForeColor = Color.White
                }
            };

            // Buttons
            this.btnPrint = new Button
            {
                Text = "طباعة",
                Location = new Point(20, 520),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnPrint.FlatAppearance.BorderSize = 0;
            this.btnPrint.Click += BtnPrint_Click;

            this.btnClose = new Button
            {
                Text = "إغلاق",
                Location = new Point(190, 520),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnClose.FlatAppearance.BorderSize = 0;
            this.btnClose.Click += (s, e) => this.Close();

            // Add controls
            this.Controls.AddRange(new Control[] {
                lblSearch,
                this.txtSearch,
                lblFrom,
                this.dtpFrom,
                lblTo,
                this.dtpTo,
                lblCustomer,
                this.cmbCustomer,
                this.btnSearch,
                this.gridSales,
                this.btnPrint,
                this.btnClose
            });
        }

        private void LoadData()
        {
            // Load customers
            var customers = dataManager.GetAllCustomers();
            this.cmbCustomer.DataSource = customers;
            this.cmbCustomer.DisplayMember = "Name";
            this.cmbCustomer.ValueMember = "Id";

            // Set default date range (last 30 days)
            this.dtpFrom.Value = DateTime.Today.AddDays(-30);
            this.dtpTo.Value = DateTime.Today;

            // Load sales
            var invoices = dataManager.GetAllInvoices();
            this.gridSales.DataSource = invoices;
            ConfigureGrid();
        }

        private void ConfigureGrid()
        {
            this.gridSales.Columns["Id"].Visible = false;
            this.gridSales.Columns["CustomerId"].Visible = false;
            this.gridSales.Columns["Items"].Visible = false;
            this.gridSales.Columns["InvoiceNumber"].HeaderText = "رقم الفاتورة";
            this.gridSales.Columns["Date"].HeaderText = "التاريخ";
            this.gridSales.Columns["CustomerName"].HeaderText = "العميل";
            this.gridSales.Columns["SubTotal"].HeaderText = "المجموع الفرعي";
            this.gridSales.Columns["Tax"].HeaderText = "الضريبة";
            this.gridSales.Columns["Discount"].HeaderText = "الخصم";
            this.gridSales.Columns["Total"].HeaderText = "الإجمالي";
            this.gridSales.Columns["Notes"].HeaderText = "ملاحظات";
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            var invoices = dataManager.GetAllInvoices();

            // Apply filters
            if (!string.IsNullOrEmpty(txtSearch.Text))
            {
                invoices = invoices.Where(i =>
                    i.InvoiceNumber.Contains(txtSearch.Text) ||
                    i.CustomerName.Contains(txtSearch.Text)
                ).ToList();
            }

            if (cmbCustomer.SelectedValue != null)
            {
                var customerId = (int)cmbCustomer.SelectedValue;
                invoices = invoices.Where(i => i.CustomerId == customerId).ToList();
            }

            invoices = invoices.Where(i =>
                i.Date.Date >= dtpFrom.Value.Date &&
                i.Date.Date <= dtpTo.Value.Date
            ).ToList();

            this.gridSales.DataSource = invoices;
            ConfigureGrid();
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            if (gridSales.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار فاتورة للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var invoice = (SimpleInvoice)gridSales.SelectedRows[0].DataBoundItem;
            using (var form = new InvoicePrintForm(invoice, dataManager))
            {
                form.ShowDialog();
            }
        }
    }
} 