using System;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;
using System.Collections.Generic;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Forms
{
    public partial class ReportsForm : Form
    {
        private readonly SimpleDataManager dataManager;
        private TabControl tabControl;
        private TabPage tabDaily;
        private TabPage tabWeekly;
        private TabPage tabMonthly;
        private DateTimePicker dtpDate;
        private DateTimePicker dtpWeekStart;
        private DateTimePicker dtpMonthStart;
        private DataGridView gridDaily;
        private DataGridView gridWeekly;
        private DataGridView gridMonthly;
        private Label lblDailyTotal;
        private Label lblWeeklyTotal;
        private Label lblMonthlyTotal;
        private Button btnPrint;
        private Button btnExport;
        private Button btnClose;

        public ReportsForm()
        {
            this.dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "تقارير المبيعات";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Tab Control
            this.tabControl = new TabControl
            {
                Location = new Point(20, 20),
                Size = new Size(940, 500),
                Font = new Font("Segoe UI", 12)
            };

            // Daily Tab
            this.tabDaily = new TabPage("تقرير يومي");
            var lblDailyDate = new Label
            {
                Text = "التاريخ:",
                Location = new Point(20, 20),
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.dtpDate = new DateTimePicker
            {
                Location = new Point(130, 20),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                Format = DateTimePickerFormat.Short
            };

            this.gridDaily = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(880, 300),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 12)
            };

            this.lblDailyTotal = new Label
            {
                Location = new Point(20, 370),
                Size = new Size(880, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.tabDaily.Controls.AddRange(new Control[] {
                lblDailyDate,
                this.dtpDate,
                this.gridDaily,
                this.lblDailyTotal
            });

            // Weekly Tab
            this.tabWeekly = new TabPage("تقرير أسبوعي");
            var lblWeekStart = new Label
            {
                Text = "بداية الأسبوع:",
                Location = new Point(20, 20),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.dtpWeekStart = new DateTimePicker
            {
                Location = new Point(180, 20),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                Format = DateTimePickerFormat.Short
            };

            this.gridWeekly = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(880, 300),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 12)
            };

            this.lblWeeklyTotal = new Label
            {
                Location = new Point(20, 370),
                Size = new Size(880, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.tabWeekly.Controls.AddRange(new Control[] {
                lblWeekStart,
                this.dtpWeekStart,
                this.gridWeekly,
                this.lblWeeklyTotal
            });

            // Monthly Tab
            this.tabMonthly = new TabPage("تقرير شهري");
            var lblMonthStart = new Label
            {
                Text = "بداية الشهر:",
                Location = new Point(20, 20),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12)
            };

            this.dtpMonthStart = new DateTimePicker
            {
                Location = new Point(180, 20),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                Format = DateTimePickerFormat.Short
            };

            this.gridMonthly = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(880, 300),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 12)
            };

            this.lblMonthlyTotal = new Label
            {
                Location = new Point(20, 370),
                Size = new Size(880, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.tabMonthly.Controls.AddRange(new Control[] {
                lblMonthStart,
                this.dtpMonthStart,
                this.gridMonthly,
                this.lblMonthlyTotal
            });

            // Add tabs
            this.tabControl.TabPages.AddRange(new TabPage[] {
                this.tabDaily,
                this.tabWeekly,
                this.tabMonthly
            });

            // Buttons
            this.btnPrint = new Button
            {
                Text = "طباعة",
                Location = new Point(20, 540),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnPrint.FlatAppearance.BorderSize = 0;
            this.btnPrint.Click += BtnPrint_Click;

            this.btnExport = new Button
            {
                Text = "تصدير Excel",
                Location = new Point(190, 540),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnExport.FlatAppearance.BorderSize = 0;
            this.btnExport.Click += BtnExport_Click;

            this.btnClose = new Button
            {
                Text = "إغلاق",
                Location = new Point(360, 540),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnClose.FlatAppearance.BorderSize = 0;
            this.btnClose.Click += (s, e) => this.Close();

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.tabControl,
                this.btnPrint,
                this.btnExport,
                this.btnClose
            });

            // Events
            this.dtpDate.ValueChanged += (s, e) => LoadDailyReport();
            this.dtpWeekStart.ValueChanged += (s, e) => LoadWeeklyReport();
            this.dtpMonthStart.ValueChanged += (s, e) => LoadMonthlyReport();
        }

        private void LoadData()
        {
            // Set default dates
            this.dtpDate.Value = DateTime.Today;
            this.dtpWeekStart.Value = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
            this.dtpMonthStart.Value = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);

            // Load initial reports
            LoadDailyReport();
            LoadWeeklyReport();
            LoadMonthlyReport();
        }

        private void LoadDailyReport()
        {
            var date = dtpDate.Value.Date;
            var invoices = dataManager.GetAllInvoices()
                .Where(i => i.Date.Date == date)
                .ToList();

            this.gridDaily.DataSource = invoices;
            ConfigureGrid(this.gridDaily);

            decimal total = invoices.Sum(i => i.Total);
            this.lblDailyTotal.Text = $"إجمالي المبيعات: {total:N2}";
        }

        private void LoadWeeklyReport()
        {
            var startDate = dtpWeekStart.Value.Date;
            var endDate = startDate.AddDays(6);
            var invoices = dataManager.GetAllInvoices()
                .Where(i => i.Date.Date >= startDate && i.Date.Date <= endDate)
                .ToList();

            this.gridWeekly.DataSource = invoices;
            ConfigureGrid(this.gridWeekly);

            decimal total = invoices.Sum(i => i.Total);
            this.lblWeeklyTotal.Text = $"إجمالي المبيعات: {total:N2}";
        }

        private void LoadMonthlyReport()
        {
            var startDate = dtpMonthStart.Value.Date;
            var endDate = startDate.AddMonths(1).AddDays(-1);
            var invoices = dataManager.GetAllInvoices()
                .Where(i => i.Date.Date >= startDate && i.Date.Date <= endDate)
                .ToList();

            this.gridMonthly.DataSource = invoices;
            ConfigureGrid(this.gridMonthly);

            decimal total = invoices.Sum(i => i.Total);
            this.lblMonthlyTotal.Text = $"إجمالي المبيعات: {total:N2}";
        }

        private void ConfigureGrid(DataGridView grid)
        {
            grid.Columns["Id"].Visible = false;
            grid.Columns["CustomerId"].Visible = false;
            grid.Columns["Items"].Visible = false;
            grid.Columns["InvoiceNumber"].HeaderText = "رقم الفاتورة";
            grid.Columns["Date"].HeaderText = "التاريخ";
            grid.Columns["CustomerName"].HeaderText = "العميل";
            grid.Columns["SubTotal"].HeaderText = "المجموع الفرعي";
            grid.Columns["Tax"].HeaderText = "الضريبة";
            grid.Columns["Discount"].HeaderText = "الخصم";
            grid.Columns["Total"].HeaderText = "الإجمالي";
            grid.Columns["Notes"].HeaderText = "ملاحظات";
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            // TODO: Implement report printing
            MessageBox.Show("سيتم تنفيذ وظيفة الطباعة قريباً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            // TODO: Implement Excel export
            MessageBox.Show("سيتم تنفيذ وظيفة التصدير قريباً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
} 