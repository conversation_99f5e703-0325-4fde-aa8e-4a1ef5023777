<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <ApplicationIcon>app.ico</ApplicationIcon>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FontAwesome.Sharp" Version="6.2.1" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
    <PackageReference Include="EPPlus" Version="6.2.10" />
    <PackageReference Include="itext7" Version="8.0.2" />
    <PackageReference Include="itext7.pdfhtml" Version="5.0.2" />
    <PackageReference Include="ClosedXML" Version="0.102.2" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />

  </ItemGroup>

  <ItemGroup>
    <None Update="app.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>