using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;
using SimpleAccounting.Models;
using SimpleAccounting.Data;

namespace SimpleAccounting.Forms
{
    public class InvoicePrintForm : Form
    {
        private readonly SimpleInvoice invoice;
        private readonly SimpleDataManager dataManager;
        private PrintDocument printDocument;
        private PrintPreviewControl previewControl;

        public InvoicePrintForm(SimpleInvoice invoice, SimpleDataManager dataManager)
        {
            this.invoice = invoice;
            this.dataManager = dataManager;
            InitializeComponent();
            InitializePrintDocument();
        }

        private void InitializeComponent()
        {
            this.Text = "طباعة الفاتورة";
            this.Width = 600;
            this.Height = 400;
            this.printDocument = new PrintDocument();
            this.previewControl = new PrintPreviewControl();
            this.previewControl.Document = printDocument;
            this.previewControl.Dock = DockStyle.Fill;
            this.Controls.Add(previewControl);
        }

        private void InitializePrintDocument()
        {
            printDocument.PrintPage += PrintDocument_PrintPage;
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            // تنفيذ طباعة الفاتورة
            var settings = dataManager.GetSettings();
            var font = new Font("Arial", 12);
            var boldFont = new Font("Arial", 12, FontStyle.Bold);
            var brush = Brushes.Black;
            var y = 50f;
            var leftMargin = 50f;
            var rightMargin = e.MarginBounds.Right - 50f;

            // طباعة معلومات الشركة
            e.Graphics.DrawString(settings.CompanyName, boldFont, brush, leftMargin, y);
            y += 30f;
            e.Graphics.DrawString(settings.CompanyAddress, font, brush, leftMargin, y);
            y += 30f;
            e.Graphics.DrawString($"هاتف: {settings.CompanyPhone}", font, brush, leftMargin, y);
            y += 30f;

            // طباعة معلومات الفاتورة
            e.Graphics.DrawString($"رقم الفاتورة: {invoice.InvoiceNumber}", boldFont, brush, leftMargin, y);
            y += 30f;
            e.Graphics.DrawString($"التاريخ: {invoice.Date.ToShortDateString()}", font, brush, leftMargin, y);
            y += 30f;
            e.Graphics.DrawString($"العميل: {invoice.CustomerName}", font, brush, leftMargin, y);
            y += 50f;

            // طباعة تفاصيل الفاتورة
            var items = dataManager.GetInvoiceItems(invoice.Id);
            var columnWidth = (rightMargin - leftMargin) / 5;
            var headers = new[] { "المنتج", "الكمية", "السعر", "الخصم", "الإجمالي" };
            var x = leftMargin;

            // طباعة رؤوس الأعمدة
            for (int i = 0; i < headers.Length; i++)
            {
                e.Graphics.DrawString(headers[i], boldFont, brush, x, y);
                x += columnWidth;
            }
            y += 30f;

            // طباعة المنتجات
            foreach (var item in items)
            {
                x = leftMargin;
                e.Graphics.DrawString(item.ProductName, font, brush, x, y);
                x += columnWidth;
                e.Graphics.DrawString(item.Quantity.ToString(), font, brush, x, y);
                x += columnWidth;
                e.Graphics.DrawString(item.UnitPrice.ToString("N2"), font, brush, x, y);
                x += columnWidth;
                e.Graphics.DrawString(item.Discount.ToString("N2"), font, brush, x, y);
                x += columnWidth;
                e.Graphics.DrawString(item.Total.ToString("N2"), font, brush, x, y);
                y += 25f;
            }

            y += 30f;
            e.Graphics.DrawString($"الإجمالي: {invoice.TotalAmount.ToString("N2")}", boldFont, brush, leftMargin, y);
            y += 30f;
            e.Graphics.DrawString($"المدفوع: {invoice.PaidAmount.ToString("N2")}", font, brush, leftMargin, y);
            y += 30f;
            e.Graphics.DrawString($"المتبقي: {invoice.RemainingAmount.ToString("N2")}", font, brush, leftMargin, y);
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            var printDialog = new PrintDialog();
            printDialog.Document = printDocument;
            if (printDialog.ShowDialog() == DialogResult.OK)
            {
                printDocument.Print();
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            Close();
        }
    }
} 