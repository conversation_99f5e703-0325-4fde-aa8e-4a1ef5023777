using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Models;
using SimpleAccounting.Data;

namespace SimpleAccounting.Forms
{
    public partial class ProductDetailsForm : Form
    {
        private readonly SimpleDataManager dataManager;
        private readonly SimpleProduct? product;
        private readonly bool isEdit;

        private TextBox txtCode;
        private TextBox txtName;
        private TextBox txtDescription;
        private TextBox txtBarcode;
        private ComboBox cmbCategory;
        private ComboBox cmbSupplier;
        private NumericUpDown numPurchasePrice;
        private NumericUpDown numPrice;
        private NumericUpDown numQuantity;
        private NumericUpDown numMinQuantity;
        private NumericUpDown numMaxQuantity;
        private TextBox txtUnit;
        private Button btnSave;
        private Button btnCancel;
        private Label lblTitle;

        public ProductDetailsForm(SimpleProduct? product = null)
        {
            this.dataManager = new SimpleDataManager();
            this.product = product;
            this.isEdit = product != null;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.txtCode = new TextBox();
            this.txtName = new TextBox();
            this.txtDescription = new TextBox();
            this.txtBarcode = new TextBox();
            this.cmbCategory = new ComboBox();
            this.cmbSupplier = new ComboBox();
            this.numPurchasePrice = new NumericUpDown();
            this.numPrice = new NumericUpDown();
            this.numQuantity = new NumericUpDown();
            this.numMinQuantity = new NumericUpDown();
            this.numMaxQuantity = new NumericUpDown();
            this.txtUnit = new TextBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.lblTitle = new Label();

            // Form settings
            this.Text = isEdit ? "تعديل منتج" : "إضافة منتج جديد";
            this.Size = new Size(600, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.White;

            // Title
            this.lblTitle.Text = isEdit ? "تعديل منتج" : "إضافة منتج جديد";
            this.lblTitle.Font = new Font("Segoe UI", 24, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTitle.Dock = DockStyle.Top;
            this.lblTitle.Height = 60;
            this.lblTitle.TextAlign = ContentAlignment.MiddleRight;
            this.lblTitle.Padding = new Padding(0, 0, 20, 0);

            // Code
            var lblCode = new Label { Text = "الكود:", Location = new Point(20, 80), AutoSize = true };
            this.txtCode.Location = new Point(20, 100);
            this.txtCode.Size = new Size(250, 30);
            this.txtCode.Font = new Font("Segoe UI", 12);

            // Name
            var lblName = new Label { Text = "الاسم:", Location = new Point(290, 80), AutoSize = true };
            this.txtName.Location = new Point(290, 100);
            this.txtName.Size = new Size(250, 30);
            this.txtName.Font = new Font("Segoe UI", 12);

            // Description
            var lblDescription = new Label { Text = "الوصف:", Location = new Point(20, 150), AutoSize = true };
            this.txtDescription.Location = new Point(20, 170);
            this.txtDescription.Size = new Size(520, 30);
            this.txtDescription.Font = new Font("Segoe UI", 12);

            // Barcode
            var lblBarcode = new Label { Text = "الباركود:", Location = new Point(20, 220), AutoSize = true };
            this.txtBarcode.Location = new Point(20, 240);
            this.txtBarcode.Size = new Size(250, 30);
            this.txtBarcode.Font = new Font("Segoe UI", 12);

            // Category
            var lblCategory = new Label { Text = "الفئة:", Location = new Point(290, 220), AutoSize = true };
            this.cmbCategory.Location = new Point(290, 240);
            this.cmbCategory.Size = new Size(250, 30);
            this.cmbCategory.Font = new Font("Segoe UI", 12);
            this.cmbCategory.DropDownStyle = ComboBoxStyle.DropDownList;

            // Supplier
            var lblSupplier = new Label { Text = "المورد:", Location = new Point(20, 290), AutoSize = true };
            this.cmbSupplier.Location = new Point(20, 310);
            this.cmbSupplier.Size = new Size(250, 30);
            this.cmbSupplier.Font = new Font("Segoe UI", 12);
            this.cmbSupplier.DropDownStyle = ComboBoxStyle.DropDownList;

            // Purchase Price
            var lblPurchasePrice = new Label { Text = "سعر الشراء:", Location = new Point(290, 290), AutoSize = true };
            this.numPurchasePrice.Location = new Point(290, 310);
            this.numPurchasePrice.Size = new Size(250, 30);
            this.numPurchasePrice.Font = new Font("Segoe UI", 12);
            this.numPurchasePrice.DecimalPlaces = 2;
            this.numPurchasePrice.Maximum = 999999;
            this.numPurchasePrice.Minimum = 0;

            // Price
            var lblPrice = new Label { Text = "سعر البيع:", Location = new Point(20, 360), AutoSize = true };
            this.numPrice.Location = new Point(20, 380);
            this.numPrice.Size = new Size(250, 30);
            this.numPrice.Font = new Font("Segoe UI", 12);
            this.numPrice.DecimalPlaces = 2;
            this.numPrice.Maximum = 999999;
            this.numPrice.Minimum = 0;

            // Quantity
            var lblQuantity = new Label { Text = "الكمية الحالية:", Location = new Point(290, 360), AutoSize = true };
            this.numQuantity.Location = new Point(290, 380);
            this.numQuantity.Size = new Size(250, 30);
            this.numQuantity.Font = new Font("Segoe UI", 12);
            this.numQuantity.Maximum = 999999;
            this.numQuantity.Minimum = 0;

            // Min Quantity
            var lblMinQuantity = new Label { Text = "الحد الأدنى:", Location = new Point(20, 430), AutoSize = true };
            this.numMinQuantity.Location = new Point(20, 450);
            this.numMinQuantity.Size = new Size(250, 30);
            this.numMinQuantity.Font = new Font("Segoe UI", 12);
            this.numMinQuantity.Maximum = 999999;
            this.numMinQuantity.Minimum = 0;

            // Max Quantity
            var lblMaxQuantity = new Label { Text = "الحد الأقصى:", Location = new Point(290, 430), AutoSize = true };
            this.numMaxQuantity.Location = new Point(290, 450);
            this.numMaxQuantity.Size = new Size(250, 30);
            this.numMaxQuantity.Font = new Font("Segoe UI", 12);
            this.numMaxQuantity.Maximum = 999999;
            this.numMaxQuantity.Minimum = 0;

            // Unit
            var lblUnit = new Label { Text = "الوحدة:", Location = new Point(20, 500), AutoSize = true };
            this.txtUnit.Location = new Point(20, 520);
            this.txtUnit.Size = new Size(250, 30);
            this.txtUnit.Font = new Font("Segoe UI", 12);

            // Buttons
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(20, 580);
            this.btnSave.Size = new Size(250, 40);
            this.btnSave.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnSave.BackColor = Color.FromArgb(40, 167, 69);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.Cursor = Cursors.Hand;
            this.btnSave.Click += BtnSave_Click;

            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(290, 580);
            this.btnCancel.Size = new Size(250, 40);
            this.btnCancel.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.Cursor = Cursors.Hand;
            this.btnCancel.Click += (s, e) => this.Close();

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                lblCode, this.txtCode,
                lblName, this.txtName,
                lblDescription, this.txtDescription,
                lblBarcode, this.txtBarcode,
                lblCategory, this.cmbCategory,
                lblSupplier, this.cmbSupplier,
                lblPurchasePrice, this.numPurchasePrice,
                lblPrice, this.numPrice,
                lblQuantity, this.numQuantity,
                lblMinQuantity, this.numMinQuantity,
                lblMaxQuantity, this.numMaxQuantity,
                lblUnit, this.txtUnit,
                this.btnSave,
                this.btnCancel
            });
        }

        private void LoadData()
        {
            // TODO: Load categories and suppliers
            this.cmbCategory.Items.Add("عام");
            this.cmbCategory.SelectedIndex = 0;

            this.cmbSupplier.Items.Add("عام");
            this.cmbSupplier.SelectedIndex = 0;

            if (isEdit && product != null)
            {
                this.txtCode.Text = product.Code;
                this.txtName.Text = product.Name;
                this.txtDescription.Text = product.Description;
                this.txtBarcode.Text = product.Barcode;
                this.numPurchasePrice.Value = (decimal)product.PurchasePrice;
                this.numPrice.Value = (decimal)product.Price;
                this.numQuantity.Value = product.Quantity;
                this.numMinQuantity.Value = product.MinQuantity;
                this.numMaxQuantity.Value = product.MaxQuantity;
                this.txtUnit.Text = product.Unit;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtCode.Text) || string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("الرجاء إدخال الكود والاسم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var newProduct = new SimpleProduct
            {
                Code = txtCode.Text,
                Name = txtName.Text,
                Description = txtDescription.Text,
                Barcode = txtBarcode.Text,
                CategoryId = 1, // TODO: Get selected category ID
                SupplierId = 1, // TODO: Get selected supplier ID
                PurchasePrice = (decimal)numPurchasePrice.Value,
                Price = (decimal)numPrice.Value,
                Quantity = (int)numQuantity.Value,
                MinQuantity = (int)numMinQuantity.Value,
                MaxQuantity = (int)numMaxQuantity.Value,
                Unit = txtUnit.Text
            };

            if (isEdit && product != null)
            {
                newProduct.Id = product.Id;
                dataManager.UpdateProduct(newProduct);
            }
            else
            {
                dataManager.AddProduct(newProduct);
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
} 