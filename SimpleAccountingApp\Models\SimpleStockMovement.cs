using System;

namespace SimpleAccounting.Models
{
    public class SimpleStockMovement
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public DateTime MovementDate { get; set; }
        public string MovementType { get; set; }
        public int QuantityChanged { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string Type { get; set; }
        public int Quantity { get; set; }
        public DateTime Date { get; set; }
    }
} 