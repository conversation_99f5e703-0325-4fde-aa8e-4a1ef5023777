using System;
using System.Windows.Forms;
using System.Drawing;
using SimpleAccounting.Data;
using SimpleAccounting.Models;
using SimpleAccounting.Forms;

namespace SimpleAccounting.Controls
{
    public partial class DashboardUserControl : UserControl
    {
        private readonly SimpleDataManager dataManager;
        private Label lblTitle;
        private Label lblTotalSales;
        private Label lblTotalPurchases;
        private Label lblTotalCustomers;
        private Label lblTotalSuppliers;
        private Label lblTotalProducts;
        private Label lblTotalStock;
        private Label lblTotalPayments;
        private Label lblTotalReceipts;
        private DataGridView gridRecentSales;
        private DataGridView gridLowStock;
        private Button btnRefresh;
        private TableLayoutPanel statsPanel;
        private TableLayoutPanel gridPanel;

        public DashboardUserControl()
        {
            try
            {
                dataManager = new SimpleDataManager();
                InitializeComponent();
                LoadData();
                this.Dock = DockStyle.Fill;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة لوحة التحكم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            try
            {
                // Initialize controls
                this.lblTitle = new Label();
                this.lblTotalSales = new Label();
                this.lblTotalPurchases = new Label();
                this.lblTotalCustomers = new Label();
                this.lblTotalSuppliers = new Label();
                this.lblTotalProducts = new Label();
                this.lblTotalStock = new Label();
                this.lblTotalPayments = new Label();
                this.lblTotalReceipts = new Label();
                this.gridRecentSales = new DataGridView();
                this.gridLowStock = new DataGridView();
                this.btnRefresh = new Button();
                this.statsPanel = new TableLayoutPanel();
                this.gridPanel = new TableLayoutPanel();

                // Setup title
                lblTitle.Text = "لوحة التحكم";
                lblTitle.Font = new Font("Arial", 16, FontStyle.Bold);
                lblTitle.Dock = DockStyle.Top;
                lblTitle.Padding = new Padding(20, 20, 20, 20);

                // Setup refresh button
                btnRefresh.Text = "تحديث";
                btnRefresh.Dock = DockStyle.Top;
                btnRefresh.Height = 40;
                btnRefresh.Click += BtnRefresh_Click;

                // Setup stats panel
                statsPanel.Dock = DockStyle.Top;
                statsPanel.Height = 120;
                statsPanel.ColumnCount = 4;
                statsPanel.RowCount = 2;
                statsPanel.Padding = new Padding(20);
                statsPanel.CellBorderStyle = TableLayoutPanelCellBorderStyle.Single;

                // Add stats labels to panel
                statsPanel.Controls.Add(CreateStatLabel("إجمالي المبيعات: 0"), 0, 0);
                statsPanel.Controls.Add(CreateStatLabel("إجمالي المشتريات: 0"), 1, 0);
                statsPanel.Controls.Add(CreateStatLabel("عدد العملاء: 0"), 2, 0);
                statsPanel.Controls.Add(CreateStatLabel("عدد الموردين: 0"), 3, 0);
                statsPanel.Controls.Add(CreateStatLabel("عدد المنتجات: 0"), 0, 1);
                statsPanel.Controls.Add(CreateStatLabel("إجمالي المخزون: 0"), 1, 1);
                statsPanel.Controls.Add(CreateStatLabel("إجمالي المدفوعات: 0"), 2, 1);
                statsPanel.Controls.Add(CreateStatLabel("إجمالي المقبوضات: 0"), 3, 1);

                // Setup grid panel
                gridPanel.Dock = DockStyle.Fill;
                gridPanel.RowCount = 2;
                gridPanel.ColumnCount = 1;
                gridPanel.Padding = new Padding(20);

                // Setup recent sales grid
                SetupDataGridView(gridRecentSales);
                gridRecentSales.CellDoubleClick += GridRecentSales_CellDoubleClick;

                // Setup low stock grid
                SetupDataGridView(gridLowStock);
                gridLowStock.CellDoubleClick += GridLowStock_CellDoubleClick;

                // Add grids to panel
                gridPanel.Controls.Add(gridRecentSales, 0, 0);
                gridPanel.Controls.Add(gridLowStock, 0, 1);

                // Add all controls to the form
                this.Controls.AddRange(new Control[] {
                    lblTitle,
                    btnRefresh,
                    statsPanel,
                    gridPanel
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة عناصر التحكم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private Label CreateStatLabel(string text)
        {
            var label = new Label
            {
                Text = text,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 10)
            };
            return label;
        }

        private void SetupDataGridView(DataGridView grid)
        {
            grid.Dock = DockStyle.Fill;
            grid.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            grid.ReadOnly = true;
            grid.AllowUserToAddRows = false;
            grid.AllowUserToDeleteRows = false;
            grid.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            grid.MultiSelect = false;
            grid.BackgroundColor = Color.White;
            grid.BorderStyle = BorderStyle.None;
            grid.RowHeadersVisible = false;
        }

        private void LoadData()
        {
            try
            {
                // Load statistics
                var stats = dataManager.GetDashboardStats();
                UpdateStatLabel(0, 0, $"إجمالي المبيعات: {stats.TotalSales:N2}");
                UpdateStatLabel(1, 0, $"إجمالي المشتريات: {stats.TotalPurchases:N2}");
                UpdateStatLabel(2, 0, $"عدد العملاء: {stats.TotalCustomers}");
                UpdateStatLabel(3, 0, $"عدد الموردين: {stats.TotalSuppliers}");
                UpdateStatLabel(0, 1, $"عدد المنتجات: {stats.TotalProducts}");
                UpdateStatLabel(1, 1, $"إجمالي المخزون: {stats.TotalStock}");
                UpdateStatLabel(2, 1, $"إجمالي المدفوعات: {stats.TotalPayments:N2}");
                UpdateStatLabel(3, 1, $"إجمالي المقبوضات: {stats.TotalReceipts:N2}");

                // Load recent sales
                var recentSales = dataManager.GetRecentSales();
                gridRecentSales.DataSource = recentSales;

                // Load low stock products
                var lowStock = dataManager.GetLowStockProducts();
                gridLowStock.DataSource = lowStock;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateStatLabel(int column, int row, string text)
        {
            if (statsPanel.GetControlFromPosition(column, row) is Label label)
            {
                label.Text = text;
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GridRecentSales_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0)
                {
                    var invoice = (SimpleInvoice)gridRecentSales.Rows[e.RowIndex].DataBoundItem;
                    ShowInvoiceDetails();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض تفاصيل الفاتورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GridLowStock_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0)
                {
                    var product = (SimpleProduct)gridLowStock.Rows[e.RowIndex].DataBoundItem;
                    ShowProductDetails();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض تفاصيل المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowInvoiceDetails()
        {
            try
            {
                var form = new InvoiceDetailsForm(new SimpleInvoice(), new SimpleDataManager());
                form.ShowDialog();
                LoadData(); // Refresh data after closing the form
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نموذج تفاصيل الفاتورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowProductDetails()
        {
            try
            {
                var form = new ProductDetailsForm(new SimpleProduct(), new SimpleDataManager());
                form.ShowDialog();
                LoadData(); // Refresh data after closing the form
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نموذج تفاصيل المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
} 