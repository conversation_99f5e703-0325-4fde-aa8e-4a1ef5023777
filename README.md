# نظام المحاسبة البسيط

نظام محاسبة بسيط لسطح المكتب مبني باستخدام C# و Windows Forms مع دعم كامل للغة العربية.

## المميزات

- واجهة مستخدم حديثة وجذابة
- دعم كامل للغة العربية (RTL)
- نظام تسجيل دخول آمن
- إدارة المبيعات والمخزون
- إدارة العملاء والموردين
- لوحة تحكم مع إحصائيات
- تقارير وإعدادات

## متطلبات النظام

- Windows 10 أو أحدث
- .NET 6.0 أو أحدث
- SQLite

## التثبيت

1. قم بتنزيل أحدث إصدار من المشروع
2. قم بتثبيت .NET 6.0 SDK إذا لم يكن مثبتاً
3. قم بتشغيل المشروع باستخدام Visual Studio أو أي IDE يدعم C#

## بيانات الدخول الافتراضية

- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## الهيكل التنظيمي للمشروع

- `Forms/` - نماذج النوافذ
- `Controls/` - عناصر التحكم المخصصة
- `Models/` - نماذج البيانات
- `Data/` - إدارة قاعدة البيانات
- `Services/` - الخدمات (مثل إدارة المستخدم الحالي)
- `Utils/` - الدوال المساعدة

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. 