using System;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;
using SimpleAccounting.Data;
using SimpleAccounting.Models;
using SimpleAccounting.Forms;

namespace SimpleAccounting.Controls
{
    public partial class InventoryUserControl : UserControl
    {
        private readonly SimpleDataManager dataManager;
        private Label lblTitle;
        private Panel pnlStats;
        private Label lblTotalProducts;
        private Label lblTotalProductsValue;
        private Label lblLowStock;
        private Label lblLowStockValue;
        private Label lblOutOfStock;
        private Label lblOutOfStockValue;
        private Label lblTotalValue;
        private Label lblTotalValueValue;
        private TextBox txtSearch;
        private DataGridView gridProducts;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnAdjust;

        public InventoryUserControl()
        {
            this.dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadData();
            this.Dock = DockStyle.Fill;
        }

        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.pnlStats = new Panel();
            this.lblTotalProducts = new Label();
            this.lblTotalProductsValue = new Label();
            this.lblLowStock = new Label();
            this.lblLowStockValue = new Label();
            this.lblOutOfStock = new Label();
            this.lblOutOfStockValue = new Label();
            this.lblTotalValue = new Label();
            this.lblTotalValueValue = new Label();
            this.txtSearch = new TextBox();
            this.gridProducts = new DataGridView();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnAdjust = new Button();

            // Title
            this.lblTitle.Text = "إدارة المخزون";
            this.lblTitle.Font = new Font("Segoe UI", 24, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTitle.Dock = DockStyle.Top;
            this.lblTitle.Height = 60;
            this.lblTitle.TextAlign = ContentAlignment.MiddleRight;
            this.lblTitle.Padding = new Padding(0, 0, 20, 0);

            // Stats Panel
            this.pnlStats.Location = new Point(20, 80);
            this.pnlStats.Size = new Size(760, 120);
            this.pnlStats.BackColor = Color.White;
            this.pnlStats.BorderStyle = BorderStyle.None;

            // Total Products
            this.lblTotalProducts.Text = "إجمالي المنتجات";
            this.lblTotalProducts.Font = new Font("Segoe UI", 12);
            this.lblTotalProducts.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTotalProducts.Location = new Point(20, 20);
            this.lblTotalProducts.Size = new Size(150, 30);
            this.lblTotalProducts.TextAlign = ContentAlignment.MiddleRight;

            this.lblTotalProductsValue.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            this.lblTotalProductsValue.ForeColor = Color.FromArgb(40, 167, 69);
            this.lblTotalProductsValue.Location = new Point(20, 50);
            this.lblTotalProductsValue.Size = new Size(150, 30);
            this.lblTotalProductsValue.TextAlign = ContentAlignment.MiddleRight;

            // Low Stock
            this.lblLowStock.Text = "منتجات قليلة";
            this.lblLowStock.Font = new Font("Segoe UI", 12);
            this.lblLowStock.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblLowStock.Location = new Point(210, 20);
            this.lblLowStock.Size = new Size(150, 30);
            this.lblLowStock.TextAlign = ContentAlignment.MiddleRight;

            this.lblLowStockValue.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            this.lblLowStockValue.ForeColor = Color.FromArgb(255, 193, 7);
            this.lblLowStockValue.Location = new Point(210, 50);
            this.lblLowStockValue.Size = new Size(150, 30);
            this.lblLowStockValue.TextAlign = ContentAlignment.MiddleRight;

            // Out of Stock
            this.lblOutOfStock.Text = "منتجات نفذت";
            this.lblOutOfStock.Font = new Font("Segoe UI", 12);
            this.lblOutOfStock.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblOutOfStock.Location = new Point(400, 20);
            this.lblOutOfStock.Size = new Size(150, 30);
            this.lblOutOfStock.TextAlign = ContentAlignment.MiddleRight;

            this.lblOutOfStockValue.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            this.lblOutOfStockValue.ForeColor = Color.FromArgb(220, 53, 69);
            this.lblOutOfStockValue.Location = new Point(400, 50);
            this.lblOutOfStockValue.Size = new Size(150, 30);
            this.lblOutOfStockValue.TextAlign = ContentAlignment.MiddleRight;

            // Total Value
            this.lblTotalValue.Text = "قيمة المخزون";
            this.lblTotalValue.Font = new Font("Segoe UI", 12);
            this.lblTotalValue.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTotalValue.Location = new Point(590, 20);
            this.lblTotalValue.Size = new Size(150, 30);
            this.lblTotalValue.TextAlign = ContentAlignment.MiddleRight;

            this.lblTotalValueValue.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            this.lblTotalValueValue.ForeColor = Color.FromArgb(0, 123, 255);
            this.lblTotalValueValue.Location = new Point(590, 50);
            this.lblTotalValueValue.Size = new Size(150, 30);
            this.lblTotalValueValue.TextAlign = ContentAlignment.MiddleRight;

            // Add stats labels to panel
            this.pnlStats.Controls.AddRange(new Control[] {
                this.lblTotalProducts,
                this.lblTotalProductsValue,
                this.lblLowStock,
                this.lblLowStockValue,
                this.lblOutOfStock,
                this.lblOutOfStockValue,
                this.lblTotalValue,
                this.lblTotalValueValue
            });

            // Search
            this.txtSearch.Location = new Point(20, 220);
            this.txtSearch.Size = new Size(300, 30);
            this.txtSearch.Font = new Font("Segoe UI", 12);
            this.txtSearch.PlaceholderText = "بحث عن منتج...";
            this.txtSearch.TextChanged += TxtSearch_TextChanged;

            // Grid
            this.gridProducts.Location = new Point(20, 270);
            this.gridProducts.Size = new Size(760, 300);
            this.gridProducts.BackgroundColor = Color.White;
            this.gridProducts.BorderStyle = BorderStyle.None;
            this.gridProducts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.gridProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.gridProducts.MultiSelect = false;
            this.gridProducts.AllowUserToAddRows = false;
            this.gridProducts.AllowUserToDeleteRows = false;
            this.gridProducts.ReadOnly = true;
            this.gridProducts.RowHeadersVisible = false;
            this.gridProducts.Font = new Font("Segoe UI", 12);
            this.gridProducts.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.gridProducts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
            this.gridProducts.ColumnHeadersDefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            this.gridProducts.ColumnHeadersHeight = 40;
            this.gridProducts.RowTemplate.Height = 40;
            this.gridProducts.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            this.gridProducts.DefaultCellStyle.SelectionForeColor = Color.White;

            // Buttons
            this.btnAdd.Text = "إضافة منتج";
            this.btnAdd.Location = new Point(20, 590);
            this.btnAdd.Size = new Size(150, 40);
            this.btnAdd.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnAdd.BackColor = Color.FromArgb(40, 167, 69);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.FlatAppearance.BorderSize = 0;
            this.btnAdd.Cursor = Cursors.Hand;
            this.btnAdd.Click += BtnAdd_Click;

            this.btnEdit.Text = "تعديل";
            this.btnEdit.Location = new Point(190, 590);
            this.btnEdit.Size = new Size(150, 40);
            this.btnEdit.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnEdit.BackColor = Color.FromArgb(0, 123, 255);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.FlatAppearance.BorderSize = 0;
            this.btnEdit.Cursor = Cursors.Hand;
            this.btnEdit.Click += BtnEdit_Click;

            this.btnDelete.Text = "حذف";
            this.btnDelete.Location = new Point(360, 590);
            this.btnDelete.Size = new Size(150, 40);
            this.btnDelete.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnDelete.BackColor = Color.FromArgb(220, 53, 69);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.FlatAppearance.BorderSize = 0;
            this.btnDelete.Cursor = Cursors.Hand;
            this.btnDelete.Click += BtnDelete_Click;

            this.btnAdjust.Text = "تسوية المخزون";
            this.btnAdjust.Location = new Point(530, 590);
            this.btnAdjust.Size = new Size(150, 40);
            this.btnAdjust.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnAdjust.BackColor = Color.FromArgb(23, 162, 184);
            this.btnAdjust.ForeColor = Color.White;
            this.btnAdjust.FlatStyle = FlatStyle.Flat;
            this.btnAdjust.FlatAppearance.BorderSize = 0;
            this.btnAdjust.Cursor = Cursors.Hand;
            this.btnAdjust.Click += BtnAdjust_Click;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.pnlStats,
                this.txtSearch,
                this.gridProducts,
                this.btnAdd,
                this.btnEdit,
                this.btnDelete,
                this.btnAdjust
            });
        }

        private void LoadData()
        {
            var products = dataManager.GetAllProducts();
            var lowStock = products.Count(p => p.Quantity <= p.MinimumQuantity && p.Quantity > 0);
            var outOfStock = products.Count(p => p.Quantity == 0);
            var totalValue = products.Sum(p => p.Quantity * p.PurchasePrice);

            this.lblTotalProductsValue.Text = products.Count.ToString();
            this.lblLowStockValue.Text = lowStock.ToString();
            this.lblOutOfStockValue.Text = outOfStock.ToString();
            this.lblTotalValueValue.Text = totalValue.ToString("N2");

            this.gridProducts.DataSource = products;
            this.gridProducts.Columns["Id"].Visible = false;
            this.gridProducts.Columns["Code"].HeaderText = "الرمز";
            this.gridProducts.Columns["Name"].HeaderText = "الاسم";
            this.gridProducts.Columns["Description"].HeaderText = "الوصف";
            this.gridProducts.Columns["Category"].HeaderText = "الفئة";
            this.gridProducts.Columns["Supplier"].HeaderText = "المورد";
            this.gridProducts.Columns["Barcode"].HeaderText = "الباركود";
            this.gridProducts.Columns["Unit"].HeaderText = "الوحدة";
            this.gridProducts.Columns["PurchasePrice"].HeaderText = "سعر الشراء";
            this.gridProducts.Columns["SellingPrice"].HeaderText = "سعر البيع";
            this.gridProducts.Columns["Quantity"].HeaderText = "الكمية";
            this.gridProducts.Columns["MinimumQuantity"].HeaderText = "الحد الأدنى";
            this.gridProducts.Columns["MaximumQuantity"].HeaderText = "الحد الأقصى";
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            var products = dataManager.SearchProducts(txtSearch.Text);
            this.gridProducts.DataSource = products;
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            using (var form = new ProductDetailsForm())
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (gridProducts.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار منتج للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var product = (SimpleProduct)gridProducts.SelectedRows[0].DataBoundItem;
            using (var form = new ProductDetailsForm(product))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (gridProducts.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار منتج للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var product = (SimpleProduct)gridProducts.SelectedRows[0].DataBoundItem;
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المنتج {product.Name}؟",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                dataManager.DeleteProduct(product.Id);
                LoadData();
            }
        }

        private void BtnAdjust_Click(object sender, EventArgs e)
        {
            if (gridProducts.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار منتج للتسوية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var product = (SimpleProduct)gridProducts.SelectedRows[0].DataBoundItem;
            using (var form = new StockAdjustmentForm(product))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void ShowStockAdjustment()
        {
            if (gridProducts.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار منتج للتسوية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var product = (SimpleProduct)gridProducts.SelectedRows[0].DataBoundItem;
            var form = new StockAdjustmentForm(product);
            form.ShowDialog();
        }
    }
} 