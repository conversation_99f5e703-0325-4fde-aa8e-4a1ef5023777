using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;
using SimpleAccounting.Data;
using SimpleAccounting.Models;
using SimpleAccounting.Forms;
using FontAwesome.Sharp;

namespace SimpleAccounting.Controls
{
    public partial class ProductsUserControl : UserControl
    {
        private readonly SimpleDataManager dataManager;
        private Label lblTitle;
        private Label lblTotalProducts;
        private Label lblTotalCategories;
        private Label lblTotalStock;
        private TextBox txtSearch;
        private ComboBox cmbCategory;
        private Button btnSearch;
        private DataGridView gridProducts;
        private Button btnNewProduct;
        private Button btnEditProduct;
        private Button btnDeleteProduct;
        private Button btnExport;

        public ProductsUserControl()
        {
            dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadData();
            this.Dock = DockStyle.Fill;
        }

        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.lblTotalProducts = new Label();
            this.lblTotalCategories = new Label();
            this.lblTotalStock = new Label();
            this.txtSearch = new TextBox();
            this.cmbCategory = new ComboBox();
            this.btnSearch = new Button();
            this.gridProducts = new DataGridView();
            this.btnNewProduct = new Button();
            this.btnEditProduct = new Button();
            this.btnDeleteProduct = new Button();
            this.btnExport = new Button();

            // إعداد العنوان
            lblTitle.Text = "المنتجات";
            lblTitle.Font = new System.Drawing.Font("Arial", 16, System.Drawing.FontStyle.Bold);
            lblTitle.Location = new System.Drawing.Point(20, 20);
            lblTitle.AutoSize = true;

            // إعداد الإحصائيات
            lblTotalProducts.Text = "عدد المنتجات: 0";
            lblTotalProducts.Location = new System.Drawing.Point(20, 60);
            lblTotalProducts.AutoSize = true;

            lblTotalCategories.Text = "عدد الفئات: 0";
            lblTotalCategories.Location = new System.Drawing.Point(200, 60);
            lblTotalCategories.AutoSize = true;

            lblTotalStock.Text = "إجمالي المخزون: 0";
            lblTotalStock.Location = new System.Drawing.Point(380, 60);
            lblTotalStock.AutoSize = true;

            // إعداد أدوات البحث
            txtSearch.PlaceholderText = "بحث...";
            txtSearch.Location = new System.Drawing.Point(20, 100);
            txtSearch.Size = new System.Drawing.Size(200, 25);
            txtSearch.TextChanged += TxtSearch_TextChanged;

            cmbCategory.Location = new System.Drawing.Point(240, 100);
            cmbCategory.Size = new System.Drawing.Size(200, 25);
            cmbCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCategory.SelectedIndexChanged += CmbCategory_SelectedIndexChanged;

            btnSearch.Text = "بحث";
            btnSearch.Location = new System.Drawing.Point(460, 100);
            btnSearch.Size = new System.Drawing.Size(100, 25);
            btnSearch.Click += BtnSearch_Click;

            // إعداد جدول المنتجات
            gridProducts.Location = new System.Drawing.Point(20, 140);
            gridProducts.Size = new System.Drawing.Size(880, 400);
            gridProducts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            gridProducts.ReadOnly = true;
            gridProducts.AllowUserToAddRows = false;
            gridProducts.AllowUserToDeleteRows = false;
            gridProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            gridProducts.MultiSelect = false;
            gridProducts.CellDoubleClick += GridProducts_CellDoubleClick;

            // إعداد الأزرار
            btnNewProduct.Text = "منتج جديد";
            btnNewProduct.Location = new System.Drawing.Point(20, 560);
            btnNewProduct.Size = new System.Drawing.Size(120, 30);
            btnNewProduct.Click += BtnNewProduct_Click;

            btnEditProduct.Text = "تعديل";
            btnEditProduct.Location = new System.Drawing.Point(150, 560);
            btnEditProduct.Size = new System.Drawing.Size(120, 30);
            btnEditProduct.Click += BtnEditProduct_Click;

            btnDeleteProduct.Text = "حذف";
            btnDeleteProduct.Location = new System.Drawing.Point(280, 560);
            btnDeleteProduct.Size = new System.Drawing.Size(120, 30);
            btnDeleteProduct.Click += BtnDeleteProduct_Click;

            btnExport.Text = "تصدير";
            btnExport.Location = new System.Drawing.Point(410, 560);
            btnExport.Size = new System.Drawing.Size(120, 30);
            btnExport.Click += BtnExport_Click;

            // إضافة عناصر التحكم
            this.Controls.Add(lblTitle);
            this.Controls.Add(lblTotalProducts);
            this.Controls.Add(lblTotalCategories);
            this.Controls.Add(lblTotalStock);
            this.Controls.Add(txtSearch);
            this.Controls.Add(cmbCategory);
            this.Controls.Add(btnSearch);
            this.Controls.Add(gridProducts);
            this.Controls.Add(btnNewProduct);
            this.Controls.Add(btnEditProduct);
            this.Controls.Add(btnDeleteProduct);
            this.Controls.Add(btnExport);
        }

        private void LoadData()
        {
            // تحميل الإحصائيات
            var stats = dataManager.GetDashboardStats();
            lblTotalProducts.Text = $"عدد المنتجات: {stats.TotalProducts}";
            lblTotalCategories.Text = $"عدد الفئات: {stats.TotalCategories}";
            lblTotalStock.Text = $"إجمالي المخزون: {stats.TotalStock}";

            // تحميل الفئات
            var categories = dataManager.GetAllCategories();
            cmbCategory.Items.Clear();
            cmbCategory.Items.Add(new { Id = 0, Name = "جميع الفئات" });
            foreach (var category in categories)
            {
                cmbCategory.Items.Add(new { category.Id, category.Name });
            }
            cmbCategory.DisplayMember = "Name";
            cmbCategory.ValueMember = "Id";
            cmbCategory.SelectedIndex = 0;

            // تحميل المنتجات
            LoadProducts();
        }

        private void LoadProducts()
        {
            var products = dataManager.GetProducts(
                txtSearch.Text,
                cmbCategory.SelectedValue != null ? (int)cmbCategory.SelectedValue : 0
            );
            gridProducts.DataSource = products;
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            LoadProducts();
        }

        private void CmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadProducts();
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            LoadProducts();
        }

        private void GridProducts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var product = (SimpleProduct)gridProducts.Rows[e.RowIndex].DataBoundItem;
                ShowProductDetails(product);
            }
        }

        private void BtnNewProduct_Click(object sender, EventArgs e)
        {
            ShowProductDetails(null);
        }

        private void BtnEditProduct_Click(object sender, EventArgs e)
        {
            if (gridProducts.SelectedRows.Count > 0)
            {
                var product = (SimpleProduct)gridProducts.SelectedRows[0].DataBoundItem;
                ShowProductDetails(product);
            }
        }

        private void BtnDeleteProduct_Click(object sender, EventArgs e)
        {
            if (gridProducts.SelectedRows.Count > 0)
            {
                var product = (SimpleProduct)gridProducts.SelectedRows[0].DataBoundItem;
                if (MessageBox.Show("هل أنت متأكد من حذف هذا المنتج؟", "تأكيد", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    dataManager.DeleteProduct(product.Id);
                    LoadProducts();
                }
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            var dialog = new SaveFileDialog();
            dialog.Filter = "Excel Files|*.xlsx";
            dialog.Title = "تصدير المنتجات";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                dataManager.ExportProductsToExcel(dialog.FileName);
                MessageBox.Show("تم تصدير المنتجات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ShowProductDetails(SimpleProduct product)
        {
            var form = new ProductDetailsForm(product, dataManager);
            form.ShowDialog();
            LoadProducts();
        }
    }
} 