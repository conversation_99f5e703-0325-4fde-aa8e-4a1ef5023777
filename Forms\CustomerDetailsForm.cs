using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Forms
{
    public partial class CustomerDetailsForm : Form
    {
        private readonly SimpleDataManager dataManager;
        private readonly SimpleCustomer customer;
        private readonly bool isEdit;

        private Label lblTitle;
        private Label lblName;
        private TextBox txtName;
        private Label lblPhone;
        private TextBox txtPhone;
        private Label lblEmail;
        private TextBox txtEmail;
        private Label lblAddress;
        private TextBox txtAddress;
        private Button btnSave;
        private Button btnCancel;

        public CustomerDetailsForm(SimpleCustomer customer = null)
        {
            this.dataManager = new SimpleDataManager();
            this.customer = customer ?? new SimpleCustomer();
            this.isEdit = customer != null;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.lblName = new Label();
            this.txtName = new TextBox();
            this.lblPhone = new Label();
            this.txtPhone = new TextBox();
            this.lblEmail = new Label();
            this.txtEmail = new TextBox();
            this.lblAddress = new Label();
            this.txtAddress = new TextBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();

            // Form
            this.Text = isEdit ? "تعديل عميل" : "إضافة عميل";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Title
            this.lblTitle.Text = isEdit ? "تعديل عميل" : "إضافة عميل";
            this.lblTitle.Font = new Font("Segoe UI", 24, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTitle.Dock = DockStyle.Top;
            this.lblTitle.Height = 60;
            this.lblTitle.TextAlign = ContentAlignment.MiddleRight;
            this.lblTitle.Padding = new Padding(0, 0, 20, 0);

            // Name
            this.lblName.Text = "الاسم";
            this.lblName.Font = new Font("Segoe UI", 12);
            this.lblName.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblName.Location = new Point(20, 80);
            this.lblName.Size = new Size(100, 30);
            this.lblName.TextAlign = ContentAlignment.MiddleRight;

            this.txtName.Location = new Point(140, 80);
            this.txtName.Size = new Size(300, 30);
            this.txtName.Font = new Font("Segoe UI", 12);
            this.txtName.BorderStyle = BorderStyle.FixedSingle;

            // Phone
            this.lblPhone.Text = "الهاتف";
            this.lblPhone.Font = new Font("Segoe UI", 12);
            this.lblPhone.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblPhone.Location = new Point(20, 130);
            this.lblPhone.Size = new Size(100, 30);
            this.lblPhone.TextAlign = ContentAlignment.MiddleRight;

            this.txtPhone.Location = new Point(140, 130);
            this.txtPhone.Size = new Size(300, 30);
            this.txtPhone.Font = new Font("Segoe UI", 12);
            this.txtPhone.BorderStyle = BorderStyle.FixedSingle;

            // Email
            this.lblEmail.Text = "البريد الإلكتروني";
            this.lblEmail.Font = new Font("Segoe UI", 12);
            this.lblEmail.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblEmail.Location = new Point(20, 180);
            this.lblEmail.Size = new Size(100, 30);
            this.lblEmail.TextAlign = ContentAlignment.MiddleRight;

            this.txtEmail.Location = new Point(140, 180);
            this.txtEmail.Size = new Size(300, 30);
            this.txtEmail.Font = new Font("Segoe UI", 12);
            this.txtEmail.BorderStyle = BorderStyle.FixedSingle;

            // Address
            this.lblAddress.Text = "العنوان";
            this.lblAddress.Font = new Font("Segoe UI", 12);
            this.lblAddress.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblAddress.Location = new Point(20, 230);
            this.lblAddress.Size = new Size(100, 30);
            this.lblAddress.TextAlign = ContentAlignment.MiddleRight;

            this.txtAddress.Location = new Point(140, 230);
            this.txtAddress.Size = new Size(300, 60);
            this.txtAddress.Font = new Font("Segoe UI", 12);
            this.txtAddress.BorderStyle = BorderStyle.FixedSingle;
            this.txtAddress.Multiline = true;

            // Buttons
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(140, 310);
            this.btnSave.Size = new Size(150, 40);
            this.btnSave.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnSave.BackColor = Color.FromArgb(40, 167, 69);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.Cursor = Cursors.Hand;
            this.btnSave.Click += BtnSave_Click;

            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(300, 310);
            this.btnCancel.Size = new Size(150, 40);
            this.btnCancel.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.Cursor = Cursors.Hand;
            this.btnCancel.Click += BtnCancel_Click;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.lblName,
                this.txtName,
                this.lblPhone,
                this.txtPhone,
                this.lblEmail,
                this.txtEmail,
                this.lblAddress,
                this.txtAddress,
                this.btnSave,
                this.btnCancel
            });
        }

        private void LoadData()
        {
            if (isEdit)
            {
                this.txtName.Text = customer.Name;
                this.txtPhone.Text = customer.Phone;
                this.txtEmail.Text = customer.Email;
                this.txtAddress.Text = customer.Address;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم العميل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return;
            }

            customer.Name = txtName.Text;
            customer.Phone = txtPhone.Text;
            customer.Email = txtEmail.Text;
            customer.Address = txtAddress.Text;

            if (isEdit)
            {
                dataManager.UpdateCustomer(customer);
            }
            else
            {
                dataManager.AddCustomer(customer);
            }

            DialogResult = DialogResult.OK;
            Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
} 