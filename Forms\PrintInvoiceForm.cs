using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Forms
{
    public partial class PrintInvoiceForm : Form
    {
        private readonly SimpleDataManager dataManager;
        private readonly SimpleInvoice invoice;
        private PrintDocument printDocument;
        private PrintPreviewControl previewControl;

        private Label lblTitle;
        private Button btnPrint;
        private Button btnClose;

        public PrintInvoiceForm(SimpleInvoice invoice)
        {
            this.dataManager = new SimpleDataManager();
            this.invoice = invoice;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "طباعة الفاتورة";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 12);

            // Title
            this.lblTitle = new Label
            {
                Text = "معاينة الفاتورة",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                Dock = DockStyle.Top,
                Height = 60,
                TextAlign = ContentAlignment.MiddleRight,
                Padding = new Padding(0, 0, 20, 0)
            };

            // Print Preview Control
            this.previewControl = new PrintPreviewControl
            {
                Dock = DockStyle.Fill,
                Zoom = 0.5
            };

            // Print Button
            this.btnPrint = new Button
            {
                Text = "طباعة",
                Dock = DockStyle.Bottom,
                Height = 50,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnPrint.Click += BtnPrint_Click;

            // Close Button
            this.btnClose = new Button
            {
                Text = "إغلاق",
                Dock = DockStyle.Bottom,
                Height = 50,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnClose.Click += BtnClose_Click;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.previewControl,
                this.btnPrint,
                this.btnClose
            });
        }

        private void LoadData()
        {
            this.printDocument = new PrintDocument();
            this.printDocument.PrintPage += PrintDocument_PrintPage;
            this.previewControl.Document = this.printDocument;
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            var settings = dataManager.GetSettings();
            var customer = dataManager.GetCustomerById(invoice.CustomerId);
            var items = dataManager.GetInvoiceItems(invoice.Id);

            var graphics = e.Graphics;
            var font = new Font("Arial", 12);
            var boldFont = new Font("Arial", 12, FontStyle.Bold);
            var titleFont = new Font("Arial", 16, FontStyle.Bold);
            var margin = 50;
            var y = margin;

            // Company Info
            graphics.DrawString(settings.CompanyName, titleFont, Brushes.Black, margin, y);
            y += 30;
            graphics.DrawString($"العنوان: {settings.CompanyAddress}", font, Brushes.Black, margin, y);
            y += 20;
            graphics.DrawString($"هاتف: {settings.CompanyPhone}", font, Brushes.Black, margin, y);
            y += 20;
            graphics.DrawString($"البريد الإلكتروني: {settings.CompanyEmail}", font, Brushes.Black, margin, y);
            y += 20;
            graphics.DrawString($"الرقم الضريبي: {settings.CompanyVAT}", font, Brushes.Black, margin, y);
            y += 40;

            // Invoice Info
            graphics.DrawString("فاتورة مبيعات", titleFont, Brushes.Black, margin, y);
            y += 30;
            graphics.DrawString($"رقم الفاتورة: {invoice.Number}", font, Brushes.Black, margin, y);
            y += 20;
            graphics.DrawString($"التاريخ: {invoice.Date:yyyy/MM/dd}", font, Brushes.Black, margin, y);
            y += 40;

            // Customer Info
            graphics.DrawString("بيانات العميل", boldFont, Brushes.Black, margin, y);
            y += 30;
            graphics.DrawString($"الاسم: {customer.Name}", font, Brushes.Black, margin, y);
            y += 20;
            graphics.DrawString($"الهاتف: {customer.Phone}", font, Brushes.Black, margin, y);
            y += 20;
            graphics.DrawString($"العنوان: {customer.Address}", font, Brushes.Black, margin, y);
            y += 40;

            // Items Table Header
            var tableX = margin;
            var tableWidth = e.PageBounds.Width - (margin * 2);
            var colWidth = tableWidth / 5;

            graphics.DrawString("المنتج", boldFont, Brushes.Black, tableX, y);
            tableX += colWidth;
            graphics.DrawString("الكمية", boldFont, Brushes.Black, tableX, y);
            tableX += colWidth;
            graphics.DrawString("سعر الوحدة", boldFont, Brushes.Black, tableX, y);
            tableX += colWidth;
            graphics.DrawString("الخصم", boldFont, Brushes.Black, tableX, y);
            tableX += colWidth;
            graphics.DrawString("الإجمالي", boldFont, Brushes.Black, tableX, y);
            y += 30;

            // Items Table
            foreach (var item in items)
            {
                tableX = margin;
                graphics.DrawString(item.Product.Name, font, Brushes.Black, tableX, y);
                tableX += colWidth;
                graphics.DrawString(item.Quantity.ToString(), font, Brushes.Black, tableX, y);
                tableX += colWidth;
                graphics.DrawString(item.UnitPrice.ToString("N2"), font, Brushes.Black, tableX, y);
                tableX += colWidth;
                graphics.DrawString(item.Discount.ToString("N2"), font, Brushes.Black, tableX, y);
                tableX += colWidth;
                graphics.DrawString(item.Total.ToString("N2"), font, Brushes.Black, tableX, y);
                y += 25;
            }

            y += 20;

            // Totals
            var totalsX = e.PageBounds.Width - margin - 200;
            graphics.DrawString("المبلغ الإجمالي:", boldFont, Brushes.Black, totalsX, y);
            graphics.DrawString(invoice.TotalAmount.ToString("N2"), font, Brushes.Black, totalsX + 150, y);
            y += 25;

            graphics.DrawString("المبلغ المدفوع:", boldFont, Brushes.Black, totalsX, y);
            graphics.DrawString(invoice.PaidAmount.ToString("N2"), font, Brushes.Black, totalsX + 150, y);
            y += 25;

            graphics.DrawString("المبلغ المتبقي:", boldFont, Brushes.Black, totalsX, y);
            graphics.DrawString(invoice.RemainingAmount.ToString("N2"), font, Brushes.Black, totalsX + 150, y);
            y += 25;

            graphics.DrawString("الحالة:", boldFont, Brushes.Black, totalsX, y);
            graphics.DrawString(invoice.Status, font, Brushes.Black, totalsX + 150, y);
            y += 40;

            // Notes
            if (!string.IsNullOrWhiteSpace(invoice.Notes))
            {
                graphics.DrawString("ملاحظات:", boldFont, Brushes.Black, margin, y);
                y += 25;
                graphics.DrawString(invoice.Notes, font, Brushes.Black, margin, y);
            }

            // Footer
            y = e.PageBounds.Height - margin;
            graphics.DrawString("شكراً لتعاملكم معنا", font, Brushes.Black, margin, y);
            y += 20;
            graphics.DrawString("نتمنى لكم تجربة تسوق ممتعة", font, Brushes.Black, margin, y);
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            using (var dialog = new PrintDialog())
            {
                dialog.Document = this.printDocument;
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    this.printDocument.Print();
                }
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
} 