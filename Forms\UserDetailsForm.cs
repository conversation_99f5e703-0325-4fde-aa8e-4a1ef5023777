using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Forms
{
    public partial class UserDetailsForm : Form
    {
        private readonly SimpleDataManager dataManager;
        public SimpleUser User { get; set; }
        private readonly bool isEditMode;

        private Label lblTitle;
        private Label lblUsername;
        private TextBox txtUsername;
        private Label lblPassword;
        private TextBox txtPassword;
        private Label lblFullName;
        private TextBox txtFullName;
        private Label lblRole;
        private ComboBox cmbRole;
        private Label lblIsActive;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;

        public UserDetailsForm(SimpleUser user = null)
        {
            this.dataManager = new SimpleDataManager();
            this.User = user;
            this.isEditMode = user != null;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "تفاصيل المستخدم";
            this.Width = 400;
            this.Height = 300;

            // Title
            this.lblTitle = new Label
            {
                Text = isEditMode ? "تعديل مستخدم" : "إضافة مستخدم",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                Dock = DockStyle.Top,
                Height = 60,
                TextAlign = ContentAlignment.MiddleRight,
                Padding = new Padding(0, 0, 20, 0)
            };

            // Username
            this.lblUsername = new Label
            {
                Text = "اسم المستخدم",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 80),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtUsername = new TextBox
            {
                Location = new Point(190, 80),
                Size = new Size(250, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Password
            this.lblPassword = new Label
            {
                Text = "كلمة المرور",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 130),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtPassword = new TextBox
            {
                Location = new Point(190, 130),
                Size = new Size(250, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle,
                PasswordChar = '●'
            };

            // Full Name
            this.lblFullName = new Label
            {
                Text = "الاسم الكامل",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 180),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtFullName = new TextBox
            {
                Location = new Point(190, 180),
                Size = new Size(250, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Role
            this.lblRole = new Label
            {
                Text = "الدور",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 230),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.cmbRole = new ComboBox
            {
                Location = new Point(190, 230),
                Size = new Size(250, 30),
                Font = new Font("Segoe UI", 12),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Items = { "مدير", "محاسب", "مستخدم" }
            };

            // Is Active
            this.lblIsActive = new Label
            {
                Text = "نشط",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 280),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.chkIsActive = new CheckBox
            {
                Location = new Point(190, 280),
                Size = new Size(30, 30),
                Font = new Font("Segoe UI", 12),
                Checked = true
            };

            // Save Button
            this.btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(190, 330),
                Size = new Size(120, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnSave.Click += BtnSave_Click;

            // Cancel Button
            this.btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(320, 330),
                Size = new Size(120, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnCancel.Click += BtnCancel_Click;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.lblUsername,
                this.txtUsername,
                this.lblPassword,
                this.txtPassword,
                this.lblFullName,
                this.txtFullName,
                this.lblRole,
                this.cmbRole,
                this.lblIsActive,
                this.chkIsActive,
                this.btnSave,
                this.btnCancel
            });
        }

        private void LoadData()
        {
            if (isEditMode)
            {
                this.txtUsername.Text = User.Username;
                this.txtUsername.ReadOnly = true;
                this.txtPassword.Visible = false;
                this.lblPassword.Visible = false;
                this.txtFullName.Text = User.FullName;
                this.cmbRole.SelectedItem = User.Role;
                this.chkIsActive.Checked = User.IsActive;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                User.FullName = txtFullName.Text;
                User.Role = cmbRole.SelectedItem.ToString();
                User.IsActive = chkIsActive.Checked;
                User.UpdatedAt = DateTime.Now;

                dataManager.UpdateUser(User);
                MessageBox.Show("تم حفظ بيانات المستخدم بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ بيانات المستخدم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
} 