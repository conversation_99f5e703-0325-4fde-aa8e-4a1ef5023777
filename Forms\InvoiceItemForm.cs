using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Models;
using SimpleAccounting.Data;

namespace SimpleAccounting.Forms
{
    public partial class InvoiceItemForm : Form
    {
        private readonly SimpleDataManager dataManager;
        private ComboBox cmbProduct;
        private NumericUpDown numQuantity;
        private NumericUpDown numUnitPrice;
        private NumericUpDown numDiscount;
        private NumericUpDown numTotal;
        private Button btnSave;
        private Button btnCancel;
        private Label lblTitle;

        public SimpleInvoiceItem Item { get; private set; }
        public SimpleInvoiceItem InvoiceItem { get; set; }

        public InvoiceItemForm()
        {
            this.dataManager = new SimpleDataManager();
            InitializeComponent();
            InvoiceItem = new SimpleInvoiceItem();
            LoadProducts();
        }

        public InvoiceItemForm(SimpleInvoiceItem item)
        {
            InitializeComponent();
            InvoiceItem = item;
            LoadItem();
        }

        private void InitializeComponent()
        {
            this.cmbProduct = new ComboBox();
            this.numQuantity = new NumericUpDown();
            this.numUnitPrice = new NumericUpDown();
            this.numDiscount = new NumericUpDown();
            this.numTotal = new NumericUpDown();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.lblTitle = new Label();

            // Form settings
            this.Text = "إضافة منتج";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.White;

            // Title
            this.lblTitle.Text = "إضافة منتج";
            this.lblTitle.Font = new Font("Segoe UI", 24, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTitle.Dock = DockStyle.Top;
            this.lblTitle.Height = 60;
            this.lblTitle.TextAlign = ContentAlignment.MiddleRight;
            this.lblTitle.Padding = new Padding(0, 0, 20, 0);

            // Product
            var lblProduct = new Label { Text = "المنتج:", Location = new Point(20, 80), AutoSize = true };
            this.cmbProduct.Location = new Point(20, 100);
            this.cmbProduct.Size = new Size(440, 30);
            this.cmbProduct.Font = new Font("Segoe UI", 12);
            this.cmbProduct.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbProduct.DisplayMember = "Name";
            this.cmbProduct.ValueMember = "Id";
            this.cmbProduct.SelectedIndexChanged += CmbProduct_SelectedIndexChanged;

            // Quantity
            var lblQuantity = new Label { Text = "الكمية:", Location = new Point(20, 150), AutoSize = true };
            this.numQuantity.Location = new Point(20, 170);
            this.numQuantity.Size = new Size(440, 30);
            this.numQuantity.Font = new Font("Segoe UI", 12);
            this.numQuantity.DecimalPlaces = 2;
            this.numQuantity.Maximum = 999999;
            this.numQuantity.Minimum = 0.01m;
            this.numQuantity.Value = 1;
            this.numQuantity.ValueChanged += (s, e) => CalculateTotal();

            // Unit Price
            var lblUnitPrice = new Label { Text = "سعر الوحدة:", Location = new Point(20, 220), AutoSize = true };
            this.numUnitPrice.Location = new Point(20, 240);
            this.numUnitPrice.Size = new Size(440, 30);
            this.numUnitPrice.Font = new Font("Segoe UI", 12);
            this.numUnitPrice.DecimalPlaces = 2;
            this.numUnitPrice.Maximum = 999999;
            this.numUnitPrice.Minimum = 0;
            this.numUnitPrice.ValueChanged += (s, e) => CalculateTotal();

            // Discount
            var lblDiscount = new Label { Text = "الخصم:", Location = new Point(20, 290), AutoSize = true };
            this.numDiscount.Location = new Point(20, 310);
            this.numDiscount.Size = new Size(440, 30);
            this.numDiscount.Font = new Font("Segoe UI", 12);
            this.numDiscount.DecimalPlaces = 2;
            this.numDiscount.Maximum = 999999;
            this.numDiscount.Minimum = 0;
            this.numDiscount.ValueChanged += (s, e) => CalculateTotal();

            // Total
            var lblTotal = new Label { Text = "الإجمالي:", Location = new Point(20, 360), AutoSize = true };
            this.numTotal.Location = new Point(20, 380);
            this.numTotal.Size = new Size(440, 30);
            this.numTotal.Font = new Font("Segoe UI", 12);
            this.numTotal.DecimalPlaces = 2;
            this.numTotal.Maximum = 999999;
            this.numTotal.ReadOnly = true;

            // Buttons
            this.btnSave.Text = "حفظ";
            this.btnSave.Location = new Point(20, 430);
            this.btnSave.Size = new Size(210, 40);
            this.btnSave.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnSave.BackColor = Color.FromArgb(40, 167, 69);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.Cursor = Cursors.Hand;
            this.btnSave.Click += BtnSave_Click;

            this.btnCancel.Text = "إلغاء";
            this.btnCancel.Location = new Point(250, 430);
            this.btnCancel.Size = new Size(210, 40);
            this.btnCancel.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.Cursor = Cursors.Hand;
            this.btnCancel.Click += (s, e) => this.Close();

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                lblProduct, this.cmbProduct,
                lblQuantity, this.numQuantity,
                lblUnitPrice, this.numUnitPrice,
                lblDiscount, this.numDiscount,
                lblTotal, this.numTotal,
                this.btnSave,
                this.btnCancel
            });
        }

        private void LoadProducts()
        {
            var products = dataManager.GetAllProducts();
            cmbProduct.DataSource = products;
        }

        private void CmbProduct_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbProduct.SelectedItem is SimpleProduct product)
            {
                numUnitPrice.Value = (decimal)product.SellingPrice;
                CalculateTotal();
            }
        }

        private void CalculateTotal()
        {
            decimal quantity = (decimal)numQuantity.Value;
            decimal unitPrice = (decimal)numUnitPrice.Value;
            decimal discount = (decimal)numDiscount.Value;
            decimal total = (quantity * unitPrice) - discount;
            numTotal.Value = total;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (cmbProduct.SelectedItem is not SimpleProduct product)
            {
                MessageBox.Show("الرجاء اختيار منتج", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (numQuantity.Value > product.Quantity)
            {
                MessageBox.Show("الكمية المطلوبة غير متوفرة في المخزون", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            Item = new SimpleInvoiceItem
            {
                ProductId = product.Id,
                ProductName = product.Name,
                ProductCode = product.Code,
                Quantity = (decimal)numQuantity.Value,
                UnitPrice = (decimal)numUnitPrice.Value,
                Discount = (decimal)numDiscount.Value,
                Total = (decimal)numTotal.Value
            };

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void LoadItem()
        {
            // Implementation of LoadItem method
        }
    }
} 