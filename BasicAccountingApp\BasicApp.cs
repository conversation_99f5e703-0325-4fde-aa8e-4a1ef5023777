using System;
using System.Drawing;
using System.Windows.Forms;

namespace BasicApp
{
    public partial class MainForm : Form
    {
        private MenuStrip menuStrip;
        private ToolStripMenuItem fileMenu;
        private ToolStripMenuItem helpMenu;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private Panel mainPanel;
        private Label welcomeLabel;

        public MainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // Form settings
            this.Text = "نظام المحاسبة البسيط - Simple Accounting System";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.Icon = SystemIcons.Application;

            // Menu Strip
            this.menuStrip = new MenuStrip();
            this.fileMenu = new ToolStripMenuItem("ملف");
            this.helpMenu = new ToolStripMenuItem("مساعدة");
            
            this.menuStrip.Items.AddRange(new ToolStripItem[] {
                this.fileMenu,
                this.helpMenu
            });

            // Status Strip
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel("جاهز");
            this.statusStrip.Items.Add(this.statusLabel);

            // Main Panel
            this.mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };

            // Welcome Label
            this.welcomeLabel = new Label
            {
                Text = "مرحباً بك في نظام المحاسبة البسيط\nSimple Accounting System",
                Font = new Font("Arial", 24, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            this.mainPanel.Controls.Add(this.welcomeLabel);

            // Add controls to form
            this.Controls.Add(this.mainPanel);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.menuStrip);
            this.MainMenuStrip = this.menuStrip;
        }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
