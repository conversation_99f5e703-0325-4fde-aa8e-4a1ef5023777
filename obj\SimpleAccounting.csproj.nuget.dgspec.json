{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\New folder (8)\\SimpleAccounting.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\New folder (8)\\SimpleAccounting.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\New folder (8)\\SimpleAccounting.csproj", "projectName": "SimpleAccounting", "projectPath": "C:\\Users\\<USER>\\Documents\\New folder (8)\\SimpleAccounting.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\New folder (8)\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"ClosedXML": {"target": "Package", "version": "[0.102.2, )"}, "EPPlus": {"target": "Package", "version": "[6.2.10, )"}, "FontAwesome.Sharp": {"target": "Package", "version": "[6.2.1, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.118, )"}, "System.Drawing.Common": {"target": "Package", "version": "[6.0.0, )"}, "System.Windows.Forms": {"target": "Package", "version": "[4.0.0, )"}, "itext7": {"target": "Package", "version": "[8.0.2, )"}, "itext7.pdfhtml": {"target": "Package", "version": "[5.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}