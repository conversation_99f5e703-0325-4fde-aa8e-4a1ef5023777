using System;

namespace SimpleAccounting.Models
{
    public class SimplePayment
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentType { get; set; }
        public string Notes { get; set; }
        public DateTime PaymentDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime Date { get; set; }
        public int SupplierId { get; set; }
    }
} 