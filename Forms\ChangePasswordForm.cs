using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Data;
using SimpleAccounting.Models;

namespace SimpleAccounting.Forms
{
    public partial class ChangePasswordForm : Form
    {
        private readonly SimpleDataManager dataManager;
        private readonly SimpleUser user;

        private Label lblTitle;
        private Label lblCurrentPassword;
        private TextBox txtCurrentPassword;
        private Label lblNewPassword;
        private TextBox txtNewPassword;
        private Label lblConfirmPassword;
        private TextBox txtConfirmPassword;
        private Button btnSave;
        private Button btnCancel;

        public ChangePasswordForm(SimpleUser user)
        {
            this.dataManager = new SimpleDataManager();
            this.user = user;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تغيير كلمة المرور";
            this.Size = new Size(500, 300);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 12);

            // Title
            this.lblTitle = new Label
            {
                Text = "تغيير كلمة المرور",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                Dock = DockStyle.Top,
                Height = 60,
                TextAlign = ContentAlignment.MiddleRight,
                Padding = new Padding(0, 0, 20, 0)
            };

            // Current Password
            this.lblCurrentPassword = new Label
            {
                Text = "كلمة المرور الحالية",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 80),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtCurrentPassword = new TextBox
            {
                Location = new Point(190, 80),
                Size = new Size(250, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle,
                PasswordChar = '●'
            };

            // New Password
            this.lblNewPassword = new Label
            {
                Text = "كلمة المرور الجديدة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 130),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtNewPassword = new TextBox
            {
                Location = new Point(190, 130),
                Size = new Size(250, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle,
                PasswordChar = '●'
            };

            // Confirm Password
            this.lblConfirmPassword = new Label
            {
                Text = "تأكيد كلمة المرور",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 180),
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            this.txtConfirmPassword = new TextBox
            {
                Location = new Point(190, 180),
                Size = new Size(250, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle,
                PasswordChar = '●'
            };

            // Save Button
            this.btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(190, 230),
                Size = new Size(120, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnSave.Click += BtnSave_Click;

            // Cancel Button
            this.btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(320, 230),
                Size = new Size(120, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnCancel.Click += BtnCancel_Click;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.lblCurrentPassword,
                this.txtCurrentPassword,
                this.lblNewPassword,
                this.txtNewPassword,
                this.lblConfirmPassword,
                this.txtConfirmPassword,
                this.btnSave,
                this.btnCancel
            });
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtCurrentPassword.Text))
                {
                    MessageBox.Show("الرجاء إدخال كلمة المرور الحالية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtNewPassword.Text))
                {
                    MessageBox.Show("الرجاء إدخال كلمة المرور الجديدة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (txtNewPassword.Text != txtConfirmPassword.Text)
                {
                    MessageBox.Show("كلمة المرور الجديدة وتأكيدها غير متطابقتين", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (txtCurrentPassword.Text != user.Password)
                {
                    MessageBox.Show("كلمة المرور الحالية غير صحيحة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                user.Password = txtNewPassword.Text;
                user.UpdatedAt = DateTime.Now;
                dataManager.UpdateUser(user);

                MessageBox.Show("تم تغيير كلمة المرور بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تغيير كلمة المرور: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
} 