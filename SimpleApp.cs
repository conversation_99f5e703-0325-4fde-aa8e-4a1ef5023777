using System;
using System.Windows.Forms;

namespace SimpleTest
{
    public partial class SimpleApp : Form
    {
        public SimpleApp()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "نظام المحاسبة البسيط";
            this.Size = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;

            var label = new Label
            {
                Text = "مرحباً بك في نظام المحاسبة البسيط",
                Font = new System.Drawing.Font("Arial", 16, System.Drawing.FontStyle.Bold),
                Location = new System.Drawing.Point(200, 200),
                Size = new System.Drawing.Size(400, 50),
                TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            };

            this.Controls.Add(label);
        }
    }

    static class SimpleProgram
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new SimpleApp());
        }
    }
}
