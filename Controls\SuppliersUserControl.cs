using System;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;
using SimpleAccounting.Data;
using SimpleAccounting.Forms;
using SimpleAccounting.Models;

namespace SimpleAccounting.Controls
{
    public partial class SuppliersUserControl : UserControl
    {
        private readonly SimpleDataManager dataManager;
        private Label lblTitle;
        private Panel pnlStats;
        private Label lblTotalSuppliers;
        private Label lblTotalSuppliersValue;
        private Label lblTotalBalance;
        private Label lblTotalBalanceValue;
        private Label lblActiveSuppliers;
        private Label lblActiveSuppliersValue;
        private Label lblInactiveSuppliers;
        private Label lblInactiveSuppliersValue;
        private TextBox txtSearch;
        private DataGridView gridSuppliers;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnPayments;

        public SuppliersUserControl()
        {
            this.dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.pnlStats = new Panel();
            this.lblTotalSuppliers = new Label();
            this.lblTotalSuppliersValue = new Label();
            this.lblTotalBalance = new Label();
            this.lblTotalBalanceValue = new Label();
            this.lblActiveSuppliers = new Label();
            this.lblActiveSuppliersValue = new Label();
            this.lblInactiveSuppliers = new Label();
            this.lblInactiveSuppliersValue = new Label();
            this.txtSearch = new TextBox();
            this.gridSuppliers = new DataGridView();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnPayments = new Button();

            // Title
            this.lblTitle.Text = "إدارة الموردين";
            this.lblTitle.Font = new Font("Segoe UI", 24, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTitle.Dock = DockStyle.Top;
            this.lblTitle.Height = 60;
            this.lblTitle.TextAlign = ContentAlignment.MiddleRight;
            this.lblTitle.Padding = new Padding(0, 0, 20, 0);

            // Stats Panel
            this.pnlStats.Location = new Point(20, 80);
            this.pnlStats.Size = new Size(760, 120);
            this.pnlStats.BackColor = Color.White;
            this.pnlStats.BorderStyle = BorderStyle.None;

            // Total Suppliers
            this.lblTotalSuppliers.Text = "إجمالي الموردين";
            this.lblTotalSuppliers.Font = new Font("Segoe UI", 12);
            this.lblTotalSuppliers.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTotalSuppliers.Location = new Point(20, 20);
            this.lblTotalSuppliers.Size = new Size(150, 30);
            this.lblTotalSuppliers.TextAlign = ContentAlignment.MiddleRight;

            this.lblTotalSuppliersValue.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            this.lblTotalSuppliersValue.ForeColor = Color.FromArgb(40, 167, 69);
            this.lblTotalSuppliersValue.Location = new Point(20, 50);
            this.lblTotalSuppliersValue.Size = new Size(150, 30);
            this.lblTotalSuppliersValue.TextAlign = ContentAlignment.MiddleRight;

            // Total Balance
            this.lblTotalBalance.Text = "إجمالي الأرصدة";
            this.lblTotalBalance.Font = new Font("Segoe UI", 12);
            this.lblTotalBalance.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTotalBalance.Location = new Point(210, 20);
            this.lblTotalBalance.Size = new Size(150, 30);
            this.lblTotalBalance.TextAlign = ContentAlignment.MiddleRight;

            this.lblTotalBalanceValue.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            this.lblTotalBalanceValue.ForeColor = Color.FromArgb(0, 123, 255);
            this.lblTotalBalanceValue.Location = new Point(210, 50);
            this.lblTotalBalanceValue.Size = new Size(150, 30);
            this.lblTotalBalanceValue.TextAlign = ContentAlignment.MiddleRight;

            // Active Suppliers
            this.lblActiveSuppliers.Text = "الموردين النشطين";
            this.lblActiveSuppliers.Font = new Font("Segoe UI", 12);
            this.lblActiveSuppliers.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblActiveSuppliers.Location = new Point(400, 20);
            this.lblActiveSuppliers.Size = new Size(150, 30);
            this.lblActiveSuppliers.TextAlign = ContentAlignment.MiddleRight;

            this.lblActiveSuppliersValue.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            this.lblActiveSuppliersValue.ForeColor = Color.FromArgb(255, 193, 7);
            this.lblActiveSuppliersValue.Location = new Point(400, 50);
            this.lblActiveSuppliersValue.Size = new Size(150, 30);
            this.lblActiveSuppliersValue.TextAlign = ContentAlignment.MiddleRight;

            // Inactive Suppliers
            this.lblInactiveSuppliers.Text = "الموردين غير النشطين";
            this.lblInactiveSuppliers.Font = new Font("Segoe UI", 12);
            this.lblInactiveSuppliers.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblInactiveSuppliers.Location = new Point(590, 20);
            this.lblInactiveSuppliers.Size = new Size(150, 30);
            this.lblInactiveSuppliers.TextAlign = ContentAlignment.MiddleRight;

            this.lblInactiveSuppliersValue.Font = new Font("Segoe UI", 16, FontStyle.Bold);
            this.lblInactiveSuppliersValue.ForeColor = Color.FromArgb(220, 53, 69);
            this.lblInactiveSuppliersValue.Location = new Point(590, 50);
            this.lblInactiveSuppliersValue.Size = new Size(150, 30);
            this.lblInactiveSuppliersValue.TextAlign = ContentAlignment.MiddleRight;

            // Add stats labels to panel
            this.pnlStats.Controls.AddRange(new Control[] {
                this.lblTotalSuppliers,
                this.lblTotalSuppliersValue,
                this.lblTotalBalance,
                this.lblTotalBalanceValue,
                this.lblActiveSuppliers,
                this.lblActiveSuppliersValue,
                this.lblInactiveSuppliers,
                this.lblInactiveSuppliersValue
            });

            // Search
            this.txtSearch.Location = new Point(20, 220);
            this.txtSearch.Size = new Size(300, 30);
            this.txtSearch.Font = new Font("Segoe UI", 12);
            this.txtSearch.PlaceholderText = "بحث عن مورد...";
            this.txtSearch.TextChanged += TxtSearch_TextChanged;

            // Grid
            this.gridSuppliers.Location = new Point(20, 270);
            this.gridSuppliers.Size = new Size(760, 300);
            this.gridSuppliers.BackgroundColor = Color.White;
            this.gridSuppliers.BorderStyle = BorderStyle.None;
            this.gridSuppliers.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.gridSuppliers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.gridSuppliers.MultiSelect = false;
            this.gridSuppliers.AllowUserToAddRows = false;
            this.gridSuppliers.AllowUserToDeleteRows = false;
            this.gridSuppliers.ReadOnly = true;
            this.gridSuppliers.RowHeadersVisible = false;
            this.gridSuppliers.Font = new Font("Segoe UI", 12);
            this.gridSuppliers.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.gridSuppliers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
            this.gridSuppliers.ColumnHeadersDefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            this.gridSuppliers.ColumnHeadersHeight = 40;
            this.gridSuppliers.RowTemplate.Height = 40;
            this.gridSuppliers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            this.gridSuppliers.DefaultCellStyle.SelectionForeColor = Color.White;

            // Buttons
            this.btnAdd.Text = "إضافة مورد";
            this.btnAdd.Location = new Point(20, 590);
            this.btnAdd.Size = new Size(150, 40);
            this.btnAdd.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnAdd.BackColor = Color.FromArgb(40, 167, 69);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.FlatAppearance.BorderSize = 0;
            this.btnAdd.Cursor = Cursors.Hand;
            this.btnAdd.Click += BtnAdd_Click;

            this.btnEdit.Text = "تعديل";
            this.btnEdit.Location = new Point(190, 590);
            this.btnEdit.Size = new Size(150, 40);
            this.btnEdit.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnEdit.BackColor = Color.FromArgb(0, 123, 255);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.FlatAppearance.BorderSize = 0;
            this.btnEdit.Cursor = Cursors.Hand;
            this.btnEdit.Click += BtnEdit_Click;

            this.btnDelete.Text = "حذف";
            this.btnDelete.Location = new Point(360, 590);
            this.btnDelete.Size = new Size(150, 40);
            this.btnDelete.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnDelete.BackColor = Color.FromArgb(220, 53, 69);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.FlatAppearance.BorderSize = 0;
            this.btnDelete.Cursor = Cursors.Hand;
            this.btnDelete.Click += BtnDelete_Click;

            this.btnPayments.Text = "المدفوعات";
            this.btnPayments.Location = new Point(530, 590);
            this.btnPayments.Size = new Size(150, 40);
            this.btnPayments.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnPayments.BackColor = Color.FromArgb(23, 162, 184);
            this.btnPayments.ForeColor = Color.White;
            this.btnPayments.FlatStyle = FlatStyle.Flat;
            this.btnPayments.FlatAppearance.BorderSize = 0;
            this.btnPayments.Cursor = Cursors.Hand;
            this.btnPayments.Click += BtnPayments_Click;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.pnlStats,
                this.txtSearch,
                this.gridSuppliers,
                this.btnAdd,
                this.btnEdit,
                this.btnDelete,
                this.btnPayments
            });
        }

        private void LoadData()
        {
            var suppliers = dataManager.GetAllSuppliers();
            var activeSuppliers = suppliers.Count(s => s.Balance > 0);
            var inactiveSuppliers = suppliers.Count(s => s.Balance <= 0);
            var totalBalance = suppliers.Sum(s => s.Balance);

            this.lblTotalSuppliersValue.Text = suppliers.Count.ToString();
            this.lblTotalBalanceValue.Text = totalBalance.ToString("N2");
            this.lblActiveSuppliersValue.Text = activeSuppliers.ToString();
            this.lblInactiveSuppliersValue.Text = inactiveSuppliers.ToString();

            this.gridSuppliers.DataSource = suppliers;
            this.gridSuppliers.Columns["Id"].Visible = false;
            this.gridSuppliers.Columns["Name"].HeaderText = "الاسم";
            this.gridSuppliers.Columns["Phone"].HeaderText = "الهاتف";
            this.gridSuppliers.Columns["Email"].HeaderText = "البريد الإلكتروني";
            this.gridSuppliers.Columns["Address"].HeaderText = "العنوان";
            this.gridSuppliers.Columns["Balance"].HeaderText = "الرصيد";
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            var suppliers = dataManager.SearchSuppliers(txtSearch.Text);
            this.gridSuppliers.DataSource = suppliers;
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            using (var form = new SupplierDetailsForm())
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (gridSuppliers.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار مورد للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var supplier = (SimpleSupplier)gridSuppliers.SelectedRows[0].DataBoundItem;
            using (var form = new SupplierDetailsForm(supplier))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (gridSuppliers.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار مورد للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var supplier = (SimpleSupplier)gridSuppliers.SelectedRows[0].DataBoundItem;
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المورد {supplier.Name}؟",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                dataManager.DeleteSupplier(supplier.Id);
                LoadData();
            }
        }

        private void BtnPayments_Click(object sender, EventArgs e)
        {
            if (gridSuppliers.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار مورد لعرض المدفوعات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var supplier = (SimpleSupplier)gridSuppliers.SelectedRows[0].DataBoundItem;
            using (var form = new SupplierPaymentsForm(supplier))
            {
                form.ShowDialog();
            }
        }
    }
} 