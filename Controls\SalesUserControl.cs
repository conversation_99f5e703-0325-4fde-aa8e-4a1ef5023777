using System;
using System.Drawing;
using System.Windows.Forms;
using System.Linq;
using SimpleAccounting.Data;
using SimpleAccounting.Models;
using SimpleAccounting.Forms;

namespace SimpleAccounting.Controls
{
    public partial class SalesUserControl : UserControl
    {
        private readonly SimpleDataManager dataManager;

        // Statistics Labels
        private Label lblTitle;
        private Label lblTotalSales;
        private Label lblTotalInvoices;
        private Label lblTotalCustomers;
        private Label lblTotalProducts;

        // Search and Filter
        private TextBox txtSearch;
        private ComboBox cmbCustomer;
        private DateTimePicker dtpFrom;
        private DateTimePicker dtpTo;
        private Button btnSearch;

        // Invoices Grid
        private DataGridView gridInvoices;

        // Buttons
        private Button btnNewInvoice;
        private Button btnEditInvoice;
        private Button btnDeleteInvoice;
        private Button btnPrintInvoice;
        private Button btnExport;

        public SalesUserControl()
        {
            this.dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Dock = DockStyle.Fill;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 12);

            // Title
            this.lblTitle = new Label
            {
                Text = "المبيعات",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                Dock = DockStyle.Top,
                Height = 60,
                TextAlign = ContentAlignment.MiddleRight,
                Padding = new Padding(0, 0, 20, 0)
            };

            // Statistics Panel
            var statsPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 100,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            // Total Sales
            this.lblTotalSales = new Label
            {
                Text = "إجمالي المبيعات: 0 ريال",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(40, 167, 69),
                Location = new Point(20, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Total Invoices
            this.lblTotalInvoices = new Label
            {
                Text = "عدد الفواتير: 0",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 123, 255),
                Location = new Point(240, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Total Customers
            this.lblTotalCustomers = new Label
            {
                Text = "عدد العملاء: 0",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 193, 7),
                Location = new Point(460, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            // Total Products
            this.lblTotalProducts = new Label
            {
                Text = "عدد المنتجات: 0",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(23, 162, 184),
                Location = new Point(680, 20),
                Size = new Size(200, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            statsPanel.Controls.AddRange(new Control[] {
                this.lblTotalSales,
                this.lblTotalInvoices,
                this.lblTotalCustomers,
                this.lblTotalProducts
            });

            // Search Panel
            var searchPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.White
            };

            // Search TextBox
            this.txtSearch = new TextBox
            {
                Location = new Point(20, 15),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.FixedSingle,
                PlaceholderText = "بحث..."
            };
            this.txtSearch.TextChanged += TxtSearch_TextChanged;

            // Customer Filter
            this.cmbCustomer = new ComboBox
            {
                Location = new Point(240, 15),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 12),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            this.cmbCustomer.SelectedIndexChanged += CmbCustomer_SelectedIndexChanged;

            // Date Range
            this.dtpFrom = new DateTimePicker
            {
                Location = new Point(460, 15),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12),
                Format = DateTimePickerFormat.Short
            };
            this.dtpFrom.ValueChanged += DtpFrom_ValueChanged;

            this.dtpTo = new DateTimePicker
            {
                Location = new Point(630, 15),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 12),
                Format = DateTimePickerFormat.Short
            };
            this.dtpTo.ValueChanged += DtpTo_ValueChanged;

            // Search Button
            this.btnSearch = new Button
            {
                Text = "بحث",
                Location = new Point(800, 15),
                Size = new Size(100, 30),
                Font = new Font("Segoe UI", 12),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnSearch.Click += BtnSearch_Click;

            searchPanel.Controls.AddRange(new Control[] {
                this.txtSearch,
                this.cmbCustomer,
                this.dtpFrom,
                this.dtpTo,
                this.btnSearch
            });

            // Invoices Grid
            this.gridInvoices = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 12),
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    BackColor = Color.FromArgb(240, 240, 240),
                    ForeColor = Color.FromArgb(64, 64, 64)
                },
                ColumnHeadersHeight = 40,
                RowTemplate = { Height = 40 },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    SelectionBackColor = Color.FromArgb(0, 123, 255),
                    SelectionForeColor = Color.White
                }
            };
            this.gridInvoices.CellDoubleClick += GridInvoices_CellDoubleClick;

            // Buttons Panel
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = Color.White
            };

            // New Invoice Button
            this.btnNewInvoice = new Button
            {
                Text = "فاتورة جديدة",
                Location = new Point(20, 10),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnNewInvoice.Click += BtnNewInvoice_Click;

            // Edit Invoice Button
            this.btnEditInvoice = new Button
            {
                Text = "تعديل",
                Location = new Point(190, 10),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnEditInvoice.Click += BtnEditInvoice_Click;

            // Delete Invoice Button
            this.btnDeleteInvoice = new Button
            {
                Text = "حذف",
                Location = new Point(360, 10),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnDeleteInvoice.Click += BtnDeleteInvoice_Click;

            // Print Invoice Button
            this.btnPrintInvoice = new Button
            {
                Text = "طباعة",
                Location = new Point(530, 10),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnPrintInvoice.Click += BtnPrintInvoice_Click;

            // Export Button
            this.btnExport = new Button
            {
                Text = "تصدير",
                Location = new Point(700, 10),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            this.btnExport.Click += BtnExport_Click;

            buttonsPanel.Controls.AddRange(new Control[] {
                this.btnNewInvoice,
                this.btnEditInvoice,
                this.btnDeleteInvoice,
                this.btnPrintInvoice,
                this.btnExport
            });

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                statsPanel,
                searchPanel,
                this.gridInvoices,
                buttonsPanel
            });
        }

        private void LoadData()
        {
            var invoices = dataManager.GetAllInvoices();
            var customers = dataManager.GetAllCustomers();
            var products = dataManager.GetAllProducts();

            // Update statistics
            this.lblTotalSales.Text = $"إجمالي المبيعات: {invoices.Sum(i => i.TotalAmount):N2} ريال";
            this.lblTotalInvoices.Text = $"عدد الفواتير: {invoices.Count}";
            this.lblTotalCustomers.Text = $"عدد العملاء: {customers.Count}";
            this.lblTotalProducts.Text = $"عدد المنتجات: {products.Count}";

            // Load customers for filter
            this.cmbCustomer.Items.Clear();
            this.cmbCustomer.Items.Add("جميع العملاء");
            foreach (var customer in customers)
            {
                this.cmbCustomer.Items.Add(customer);
            }
            this.cmbCustomer.DisplayMember = "Name";
            this.cmbCustomer.SelectedIndex = 0;

            // Load invoices
            this.gridInvoices.DataSource = invoices;
            this.gridInvoices.Columns["Id"].Visible = false;
            this.gridInvoices.Columns["CustomerId"].Visible = false;
            this.gridInvoices.Columns["Customer"].HeaderText = "العميل";
            this.gridInvoices.Columns["Date"].HeaderText = "التاريخ";
            this.gridInvoices.Columns["Number"].HeaderText = "رقم الفاتورة";
            this.gridInvoices.Columns["TotalAmount"].HeaderText = "المبلغ الإجمالي";
            this.gridInvoices.Columns["PaidAmount"].HeaderText = "المبلغ المدفوع";
            this.gridInvoices.Columns["RemainingAmount"].HeaderText = "المبلغ المتبقي";
            this.gridInvoices.Columns["Status"].HeaderText = "الحالة";
            this.gridInvoices.Columns["Notes"].HeaderText = "ملاحظات";
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            var invoices = dataManager.GetAllInvoices();
            var filtered = invoices.Where(i =>
                i.Number.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                i.Customer.Name.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                i.Notes.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase)
            ).ToList();
            this.gridInvoices.DataSource = filtered;
        }

        private void CmbCustomer_SelectedIndexChanged(object sender, EventArgs e)
        {
            var invoices = dataManager.GetAllInvoices();
            var filtered = invoices;
            if (cmbCustomer.SelectedIndex > 0)
            {
                var customer = (SimpleCustomer)cmbCustomer.SelectedItem;
                filtered = invoices.Where(i => i.CustomerId == customer.Id).ToList();
            }
            this.gridInvoices.DataSource = filtered;
        }

        private void DtpFrom_ValueChanged(object sender, EventArgs e)
        {
            FilterByDateRange();
        }

        private void DtpTo_ValueChanged(object sender, EventArgs e)
        {
            FilterByDateRange();
        }

        private void FilterByDateRange()
        {
            var invoices = dataManager.GetAllInvoices();
            var filtered = invoices.Where(i =>
                i.Date.Date >= dtpFrom.Value.Date &&
                i.Date.Date <= dtpTo.Value.Date
            ).ToList();
            this.gridInvoices.DataSource = filtered;
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            var invoices = dataManager.GetAllInvoices();
            var filtered = invoices.Where(i =>
                i.Date.Date >= dtpFrom.Value.Date &&
                i.Date.Date <= dtpTo.Value.Date
            ).ToList();

            if (cmbCustomer.SelectedIndex > 0)
            {
                var customer = (SimpleCustomer)cmbCustomer.SelectedItem;
                filtered = filtered.Where(i => i.CustomerId == customer.Id).ToList();
            }

            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                filtered = filtered.Where(i =>
                    i.Number.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                    i.Customer.Name.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                    i.Notes.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase)
                ).ToList();
            }

            this.gridInvoices.DataSource = filtered;
        }

        private void GridInvoices_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var invoice = (SimpleInvoice)gridInvoices.Rows[e.RowIndex].DataBoundItem;
                using (var form = new InvoiceDetailsForm(invoice, dataManager))
                {
                    form.ShowDialog();
                    LoadData();
                }
            }
        }

        private void BtnNewInvoice_Click(object sender, EventArgs e)
        {
            using (var form = new InvoiceDetailsForm(null, dataManager))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void BtnEditInvoice_Click(object sender, EventArgs e)
        {
            if (gridInvoices.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار فاتورة للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var invoice = (SimpleInvoice)gridInvoices.SelectedRows[0].DataBoundItem;
            using (var form = new InvoiceDetailsForm(invoice, dataManager))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
        }

        private void BtnDeleteInvoice_Click(object sender, EventArgs e)
        {
            if (gridInvoices.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار فاتورة للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var invoice = (SimpleInvoice)gridInvoices.SelectedRows[0].DataBoundItem;
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الفاتورة رقم {invoice.Number}؟",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                dataManager.DeleteInvoice(invoice.Id);
                LoadData();
            }
        }

        private void BtnPrintInvoice_Click(object sender, EventArgs e)
        {
            if (gridInvoices.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار فاتورة للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var invoice = (SimpleInvoice)gridInvoices.SelectedRows[0].DataBoundItem;
            using (var form = new PrintInvoiceForm(invoice))
            {
                form.ShowDialog();
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            using (var dialog = new SaveFileDialog())
            {
                dialog.Filter = "ملفات Excel|*.xlsx";
                dialog.Title = "تصدير الفواتير";
                dialog.FileName = $"فواتير_{DateTime.Now:yyyy-MM-dd}.xlsx";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        dataManager.ExportInvoicesToExcel(dialog.FileName);
                        MessageBox.Show("تم تصدير الفواتير بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"حدث خطأ أثناء تصدير الفواتير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
    }
} 