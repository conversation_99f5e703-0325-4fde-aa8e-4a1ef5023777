using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Services;
using FontAwesome.Sharp;

namespace SimpleAccounting.Forms
{
    public partial class LoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblError;
        private Label lblTitle;
        private IconPictureBox iconUser;
        private IconPictureBox iconPassword;
        private Panel mainPanel;
        private int loginAttempts = 0;
        private const int MaxLoginAttempts = 3;

        public LoginForm()
        {
            try
            {
                InitializeComponent();
                SetupForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة النموذج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            try
            {
                this.txtUsername = new TextBox();
                this.txtPassword = new TextBox();
                this.btnLogin = new Button();
                this.lblError = new Label();
                this.lblTitle = new Label();
                this.iconUser = new IconPictureBox();
                this.iconPassword = new IconPictureBox();
                this.mainPanel = new Panel();

                // Form settings
                this.Text = "تسجيل الدخول";
                this.StartPosition = FormStartPosition.CenterScreen;
                this.Size = new Size(400, 500);
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.RightToLeft = RightToLeft.Yes;
                this.RightToLeftLayout = true;
                this.BackColor = Color.White;
                this.Icon = SystemIcons.Application;

                // Main Panel
                this.mainPanel.Dock = DockStyle.Fill;
                this.mainPanel.Padding = new Padding(20);

                // Title
                this.lblTitle.Text = "نظام المحاسبة";
                this.lblTitle.Font = new Font("Segoe UI", 24, FontStyle.Bold);
                this.lblTitle.TextAlign = ContentAlignment.MiddleCenter;
                this.lblTitle.Dock = DockStyle.Top;
                this.lblTitle.Height = 80;
                this.lblTitle.ForeColor = Color.FromArgb(64, 64, 64);

                // Username
                this.iconUser.IconChar = IconChar.User;
                this.iconUser.IconColor = Color.FromArgb(64, 64, 64);
                this.iconUser.IconSize = 24;
                this.iconUser.Location = new Point(20, 150);
                this.iconUser.Size = new Size(24, 24);

                this.txtUsername.Location = new Point(50, 150);
                this.txtUsername.Size = new Size(300, 30);
                this.txtUsername.Font = new Font("Segoe UI", 12);
                this.txtUsername.PlaceholderText = "اسم المستخدم";
                this.txtUsername.MaxLength = 50;

                // Password
                this.iconPassword.IconChar = IconChar.Lock;
                this.iconPassword.IconColor = Color.FromArgb(64, 64, 64);
                this.iconPassword.IconSize = 24;
                this.iconPassword.Location = new Point(20, 200);
                this.iconPassword.Size = new Size(24, 24);

                this.txtPassword.Location = new Point(50, 200);
                this.txtPassword.Size = new Size(300, 30);
                this.txtPassword.Font = new Font("Segoe UI", 12);
                this.txtPassword.PasswordChar = '●';
                this.txtPassword.PlaceholderText = "كلمة المرور";
                this.txtPassword.MaxLength = 50;

                // Login Button
                this.btnLogin.Text = "تسجيل الدخول";
                this.btnLogin.Location = new Point(50, 260);
                this.btnLogin.Size = new Size(300, 40);
                this.btnLogin.Font = new Font("Segoe UI", 12, FontStyle.Bold);
                this.btnLogin.BackColor = Color.FromArgb(0, 122, 204);
                this.btnLogin.ForeColor = Color.White;
                this.btnLogin.FlatStyle = FlatStyle.Flat;
                this.btnLogin.FlatAppearance.BorderSize = 0;
                this.btnLogin.Cursor = Cursors.Hand;

                // Error Label
                this.lblError.AutoSize = true;
                this.lblError.Location = new Point(50, 310);
                this.lblError.Size = new Size(300, 20);
                this.lblError.Font = new Font("Segoe UI", 10);
                this.lblError.ForeColor = Color.Red;
                this.lblError.TextAlign = ContentAlignment.MiddleCenter;
                this.lblError.Visible = false;

                // Add controls to main panel
                this.mainPanel.Controls.AddRange(new Control[] {
                    this.lblTitle,
                    this.iconUser,
                    this.txtUsername,
                    this.iconPassword,
                    this.txtPassword,
                    this.btnLogin,
                    this.lblError
                });

                // Add main panel to form
                this.Controls.Add(this.mainPanel);

                // Events
                this.btnLogin.Click += BtnLogin_Click;
                this.txtPassword.KeyPress += TxtPassword_KeyPress;
                this.txtUsername.KeyPress += TxtUsername_KeyPress;
                this.FormClosing += LoginForm_FormClosing;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة عناصر التحكم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupForm()
        {
            try
            {
                // Add any additional setup here
                this.ActiveControl = txtUsername;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إعداد النموذج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    ShowError("الرجاء إدخال اسم المستخدم وكلمة المرور");
                    return;
                }

                if (ValidateLogin())
                {
                    CurrentUser.SetUser("admin", "مدير النظام");
                    this.Hide();
                    var mainForm = new MainForm();
                    mainForm.FormClosed += (s, args) => this.Close();
                    mainForm.Show();
                }
                else
                {
                    loginAttempts++;
                    if (loginAttempts >= MaxLoginAttempts)
                    {
                        MessageBox.Show("تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. سيتم إغلاق البرنامج.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.Close();
                    }
                    else
                    {
                        ShowError($"اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: {MaxLoginAttempts - loginAttempts}");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    e.Handled = true;
                    btnLogin.PerformClick();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء معالجة المفتاح: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TxtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            try
            {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    e.Handled = true;
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء معالجة المفتاح: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoginForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (e.CloseReason == CloseReason.UserClosing)
                {
                    var result = MessageBox.Show("هل أنت متأكد من إغلاق البرنامج؟", "تأكيد", 
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.No)
                    {
                        e.Cancel = true;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إغلاق النموذج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowError(string message)
        {
            lblError.Text = message;
            lblError.Visible = true;
            txtPassword.Clear();
            txtPassword.Focus();
        }

        private bool ValidateLogin()
        {
            return txtUsername.Text.Trim() == "admin" && txtPassword.Text.Trim() == "admin123";
        }
    }
} 