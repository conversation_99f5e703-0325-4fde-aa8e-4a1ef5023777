using System;
using System.Drawing;
using System.Windows.Forms;
using SimpleAccounting.Data;
using SimpleAccounting.Forms;
using SimpleAccounting.Models;

namespace SimpleAccounting.Controls
{
    public partial class InvoicesUserControl : UserControl
    {
        private readonly SimpleDataManager dataManager;
        private TextBox txtSearch;
        private DataGridView gridInvoices;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnDelete;
        private Button btnPrint;
        private Label lblTitle;

        public InvoicesUserControl()
        {
            this.dataManager = new SimpleDataManager();
            InitializeComponent();
            LoadInvoices();
        }

        private void InitializeComponent()
        {
            this.txtSearch = new TextBox();
            this.gridInvoices = new DataGridView();
            this.btnAdd = new Button();
            this.btnEdit = new Button();
            this.btnDelete = new Button();
            this.btnPrint = new Button();
            this.lblTitle = new Label();

            // Title
            this.lblTitle.Text = "إدارة الفواتير";
            this.lblTitle.Font = new Font("Segoe UI", 24, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            this.lblTitle.Dock = DockStyle.Top;
            this.lblTitle.Height = 60;
            this.lblTitle.TextAlign = ContentAlignment.MiddleRight;
            this.lblTitle.Padding = new Padding(0, 0, 20, 0);

            // Search
            this.txtSearch.Location = new Point(20, 80);
            this.txtSearch.Size = new Size(300, 30);
            this.txtSearch.Font = new Font("Segoe UI", 12);
            this.txtSearch.PlaceholderText = "بحث عن فاتورة...";
            this.txtSearch.TextChanged += TxtSearch_TextChanged;

            // Grid
            this.gridInvoices.Location = new Point(20, 130);
            this.gridInvoices.Size = new Size(760, 400);
            this.gridInvoices.BackgroundColor = Color.White;
            this.gridInvoices.BorderStyle = BorderStyle.None;
            this.gridInvoices.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.gridInvoices.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.gridInvoices.MultiSelect = false;
            this.gridInvoices.AllowUserToAddRows = false;
            this.gridInvoices.AllowUserToDeleteRows = false;
            this.gridInvoices.ReadOnly = true;
            this.gridInvoices.RowHeadersVisible = false;
            this.gridInvoices.Font = new Font("Segoe UI", 12);
            this.gridInvoices.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.gridInvoices.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
            this.gridInvoices.ColumnHeadersDefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            this.gridInvoices.ColumnHeadersHeight = 40;
            this.gridInvoices.RowTemplate.Height = 40;
            this.gridInvoices.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 123, 255);
            this.gridInvoices.DefaultCellStyle.SelectionForeColor = Color.White;

            // Buttons
            this.btnAdd.Text = "إضافة فاتورة";
            this.btnAdd.Location = new Point(20, 550);
            this.btnAdd.Size = new Size(150, 40);
            this.btnAdd.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnAdd.BackColor = Color.FromArgb(40, 167, 69);
            this.btnAdd.ForeColor = Color.White;
            this.btnAdd.FlatStyle = FlatStyle.Flat;
            this.btnAdd.FlatAppearance.BorderSize = 0;
            this.btnAdd.Cursor = Cursors.Hand;
            this.btnAdd.Click += BtnAdd_Click;

            this.btnEdit.Text = "تعديل";
            this.btnEdit.Location = new Point(190, 550);
            this.btnEdit.Size = new Size(150, 40);
            this.btnEdit.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnEdit.BackColor = Color.FromArgb(0, 123, 255);
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.FlatAppearance.BorderSize = 0;
            this.btnEdit.Cursor = Cursors.Hand;
            this.btnEdit.Click += BtnEdit_Click;

            this.btnDelete.Text = "حذف";
            this.btnDelete.Location = new Point(360, 550);
            this.btnDelete.Size = new Size(150, 40);
            this.btnDelete.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnDelete.BackColor = Color.FromArgb(220, 53, 69);
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.FlatAppearance.BorderSize = 0;
            this.btnDelete.Cursor = Cursors.Hand;
            this.btnDelete.Click += BtnDelete_Click;

            this.btnPrint.Text = "طباعة";
            this.btnPrint.Location = new Point(530, 550);
            this.btnPrint.Size = new Size(150, 40);
            this.btnPrint.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.btnPrint.BackColor = Color.FromArgb(23, 162, 184);
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.FlatAppearance.BorderSize = 0;
            this.btnPrint.Cursor = Cursors.Hand;
            this.btnPrint.Click += BtnPrint_Click;

            // Add controls
            this.Controls.AddRange(new Control[] {
                this.lblTitle,
                this.txtSearch,
                this.gridInvoices,
                this.btnAdd,
                this.btnEdit,
                this.btnDelete,
                this.btnPrint
            });
        }

        private void LoadInvoices()
        {
            var invoices = dataManager.GetAllInvoices();
            gridInvoices.DataSource = invoices;
            gridInvoices.Columns["Id"].Visible = false;
            gridInvoices.Columns["CustomerId"].Visible = false;
            gridInvoices.Columns["Items"].Visible = false;
            gridInvoices.Columns["InvoiceNumber"].HeaderText = "رقم الفاتورة";
            gridInvoices.Columns["Date"].HeaderText = "التاريخ";
            gridInvoices.Columns["CustomerName"].HeaderText = "العميل";
            gridInvoices.Columns["SubTotal"].HeaderText = "المجموع الفرعي";
            gridInvoices.Columns["Tax"].HeaderText = "الضريبة";
            gridInvoices.Columns["Discount"].HeaderText = "الخصم";
            gridInvoices.Columns["Total"].HeaderText = "الإجمالي";
            gridInvoices.Columns["Notes"].HeaderText = "ملاحظات";
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            var invoices = dataManager.SearchInvoices(txtSearch.Text);
            gridInvoices.DataSource = invoices;
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            using (var form = new InvoiceDetailsForm())
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadInvoices();
                }
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (gridInvoices.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار فاتورة للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var invoice = (SimpleInvoice)gridInvoices.SelectedRows[0].DataBoundItem;
            using (var form = new InvoiceDetailsForm(invoice))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadInvoices();
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (gridInvoices.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار فاتورة للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var invoice = (SimpleInvoice)gridInvoices.SelectedRows[0].DataBoundItem;
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الفاتورة {invoice.InvoiceNumber}؟",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                dataManager.DeleteInvoice(invoice.Id);
                LoadInvoices();
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            if (gridInvoices.SelectedRows.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار فاتورة للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var invoice = (SimpleInvoice)gridInvoices.SelectedRows[0].DataBoundItem;
            using (var form = new InvoicePrintForm(invoice))
            {
                form.ShowDialog();
            }
        }
    }
} 